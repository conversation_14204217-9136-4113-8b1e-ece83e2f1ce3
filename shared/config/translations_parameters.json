{"turn_on_all": {"en_US": "Turn on all", "it_IT": "Accendi tutto"}, "turn_off_all": {"en_US": "Turn off all", "it_IT": "<PERSON><PERSON><PERSON> tutto"}, "total_kwh": {"en_US": "Total kWh", "it_IT": "kWh Totali"}, "window_covering_type": {"en_US": "Window covering type", "it_IT": "<PERSON><PERSON><PERSON> cope<PERSON>ura <PERSON>ra"}, "window_covering_command_stop": {"en_US": "Stop command", "it_IT": "Comando stop"}, "window_covering_command_down": {"en_US": "Down command", "it_IT": "<PERSON><PERSON><PERSON> gi<PERSON>"}, "window_covering_command_up": {"en_US": "Up command", "it_IT": "Comando su"}, "window_covering_percentage": {"en_US": "Window covering percentage", "it_IT": "Per<PERSON><PERSON><PERSON> copertura <PERSON>ra"}, "ssid": {"en_US": "WiFi network", "it_IT": "Rete WiFi"}, "fall": {"en_US": "Fall", "it_IT": "<PERSON><PERSON><PERSON>"}, "presence": {"en_US": "Presence", "it_IT": "Presenza"}, "movement": {"en_US": "Movement", "it_IT": "Movimento"}, "sound_average": {"en_US": "Sound average", "it_IT": "Media sonora"}, "sound_peak": {"en_US": "Sound peak", "it_IT": "<PERSON><PERSON> sonoro"}, "phase_1_line_to_neutral_volts": {"en_US": "Phase 1 Line to Neutral Volts", "it_IT": "Volt Linea Fase 1 a Neutro"}, "input_1": {"en_US": "Input 1", "it_IT": "Input 1"}, "input_2": {"en_US": "Input 2", "it_IT": "Input 2"}, "input_3": {"en_US": "Input 3", "it_IT": "Input 3"}, "input_4": {"en_US": "Input 4", "it_IT": "Input 4"}, "input_1_rescaled": {"en_US": "Input 1 Rescaled", "it_IT": "Input 1 Riscalato "}, "input_2_rescaled": {"en_US": "Input 2 Rescaled", "it_IT": "Input 2 Riscalato "}, "input_3_rescaled": {"en_US": "Input 3 Rescaled", "it_IT": "Input 3 Riscalato "}, "input_4_rescaled": {"en_US": "Input 4 Rescaled", "it_IT": "Input 4 Riscalato "}, "active_power": {"en_US": "Active Power", "it_IT": "Potenza Attiva"}, "apparent_power": {"en_US": "Apparent Power", "it_IT": "Potenza Apparente"}, "current": {"en_US": "Current", "it_IT": "<PERSON><PERSON><PERSON>"}, "current_demand": {"en_US": "Current Demand", "it_IT": "<PERSON><PERSON><PERSON>"}, "current_system_positive_power_demand": {"en_US": "Current System Positive Power Demand", "it_IT": "Richiesta Corrente Potenza Positiva Sistema"}, "current_system_reverse_power_demand": {"en_US": "Current System Reverse Power Demand", "it_IT": "Richiesta Corrente Potenza Inversa Sistema"}, "export_active_energy": {"en_US": "Export Active Energy", "it_IT": "Esporta Energia Attiva"}, "export_reactive_energy": {"en_US": "Export Reactive Energy", "it_IT": "Esporta Energia Reattiva"}, "frequency": {"en_US": "Frequency", "it_IT": "Frequenza"}, "import_active_energy": {"en_US": "Import Active Energy", "it_IT": "Importa Energia Attiva"}, "import_reactive_energy": {"en_US": "Import Reactive Energy", "it_IT": "Importa Energia Reattiva"}, "line_to_neutral_volts": {"en_US": "Line to Neutral Volts", "it_IT": "Volt Linea a neutro"}, "maximum_current_demand": {"en_US": "Maximum Current Demand", "it_IT": "Massima Corrente <PERSON>"}, "maximum_system_positive_power_demand": {"en_US": "Maximum System Positive Power Demand", "it_IT": "Massima Richiesta Potenza Positiva Sistema"}, "maximum_system_reverse_power_demand": {"en_US": "Maximum System Reverse Power Demand", "it_IT": "Massima Richiesta Potenza Inversa Sistema"}, "phase_angle": {"en_US": "Phase Angle", "it_IT": "<PERSON><PERSON>"}, "power_factor": {"en_US": "Power Factor", "it_IT": "<PERSON><PERSON>"}, "reactive_power": {"en_US": "Reactive Power", "it_IT": "Potenza Reattiva"}, "phase_2_line_to_neutral_volts": {"en_US": "Phase 2 Line to Neutral Volts", "it_IT": "Volt Linea Fase 2 a Neutro"}, "phase_3_line_to_neutral_volts": {"en_US": "Phase 3 Line to Neutral Volts", "it_IT": "Volt Linea Fase 3 a Neutro"}, "phase_1_current": {"en_US": "Phase 1 Current", "it_IT": "Corrente Fase 1"}, "phase_2_current": {"en_US": "Phase 2 Current", "it_IT": "Corrente Fase 2"}, "phase_3_current": {"en_US": "Phase 3 Current", "it_IT": "Corrente Fase 3"}, "phase_1_power": {"en_US": "Phase 1 Power", "it_IT": "Potenza Fase 1"}, "phase_2_power": {"en_US": "Phase 2 Power", "it_IT": "Potenza Fase 2"}, "phase_3_power": {"en_US": "Phase 3 Power", "it_IT": "Potenza Fase 3"}, "phase_1_volt_amps": {"en_US": "Phase 1 Voltampere", "it_IT": "Voltampere Fase 1"}, "phase_2_volt_amps": {"en_US": "Phase 2 Voltampere", "it_IT": "Voltampere Fase 2"}, "phase_3_volt_amps": {"en_US": "Phase 3 Voltampere", "it_IT": "Voltampere Fase 3"}, "phase_1_volt_amps_reactive": {"en_US": "Phase 1 Voltampere Reactive", "it_IT": "Voltampere Reattivi Fase 1"}, "phase_2_volt_amps_reactive": {"en_US": "Phase 2 Voltampere Reactive", "it_IT": "Voltampere Reattivi Fase 2"}, "phase_3_volt_amps_reactive": {"en_US": "Phase 3 Voltampere Reactive", "it_IT": "Voltampere Reattivi Fase 3"}, "phase_1_power_factor": {"en_US": "Phase 1 Power Factor", "it_IT": "Fattore Potenza Fase 1"}, "phase_2_power_factor": {"en_US": "Phase 2 Power Factor", "it_IT": "Fattore Potenza Fase 2"}, "phase_3_power_factor": {"en_US": "Phase 3 Power Factor", "it_IT": "Fattore Potenza Fase 3"}, "phase_1_phase_angle": {"en_US": "Phase 1 Phase Angle", "it_IT": "Angolo Fase 1"}, "phase_2_phase_angle": {"en_US": "Phase 2 Phase Angle", "it_IT": "Angolo Fase 2"}, "phase_3_phase_angle": {"en_US": "Phase 3 Phase Angle", "it_IT": "Angolo Fase 3"}, "average_line_to_neutral_volts": {"en_US": "Average Line to Neutral Volts", "it_IT": "Volt medi da Linea a Neutro"}, "average_line_current": {"en_US": "Average Line Current", "it_IT": "Corrente media Linea"}, "sum_of_line_currents": {"en_US": "Sum of Line Currents", "it_IT": "Somma corrente Linee"}, "total_system_power": {"en_US": "Total System Power", "it_IT": "Potenza Totale Sistema"}, "total_system_voltamps": {"en_US": "Total System Voltampere", "it_IT": "Voltampere Totali Sistema"}, "total_system_var": {"en_US": "Total System Voltampere Reactive", "it_IT": "Voltampere Reattivi Totali Sistema"}, "total_system_power_factor": {"en_US": "Total System Power Factor", "it_IT": "Fattore Potenza Totale Sistema"}, "total_system_phase_angle": {"en_US": "Total System Phase Angle", "it_IT": "Angolo Fase Totale Sistema"}, "frequency_of_supply_voltages": {"en_US": "Frequency of supply Voltages", "it_IT": "Frequenza di Tensione di Alimentazione"}, "total_system_power_demand": {"en_US": "Total System Power Demand", "it_IT": "Potenza Richiesta Totale Sistema"}, "maximum_total_system_power_demand": {"en_US": "Maximum Total System Power Demand", "it_IT": "Massima Potenza Richiesta Totale Sistema"}, "total_system_va_demand": {"en_US": "Total System Voltampere Demand", "it_IT": "Voltampere Totali Richiesta Sistema"}, "maximum_total_system_va_demand": {"en_US": "Maximum Total System Voltampere Demand", "it_IT": "Voltampere Massimi Totali Richiesta Sistema"}, "neutral_current_demand": {"en_US": "Neutral Current Demand", "it_IT": "Richiesta Corrente Neutro"}, "maximum_neutral_current_demand": {"en_US": "Maximum Neutral Current Demand", "it_IT": "Richiesta Corrente Neutro Massima"}, "line_1_to_line_2_volts": {"en_US": "Line 1 to Line 2 Volts", "it_IT": "Volt da Linea 1 a Linea 2"}, "line_2_to_line_3_volts": {"en_US": "Line 2 to Line 3 Volts", "it_IT": "Volt da Linea 2 a Linea 3"}, "line_3_to_line_1_volts": {"en_US": "Line 3 to Line 1 Volts", "it_IT": "Volt da Linea 3 a Linea 1"}, "average_line_to_line_volts": {"en_US": "Average Line to Line Volts", "it_IT": "Volt da Linea a Linea medi"}, "neutral_current": {"en_US": "Neutral Current", "it_IT": "<PERSON><PERSON><PERSON>"}, "phase_1_l/n_volts_thd": {"en_US": "Phase 1 L/N Volts THD", "it_IT": "Volt Fase 1 L/N THD"}, "phase_2_l/n_volts_thd": {"en_US": "Phase 2 L/N Volts THD", "it_IT": "Volt Fase 2 L/N THD"}, "phase_3_l/n_volts_thd": {"en_US": "Phase 3 L/N Volts THD", "it_IT": "Volt Fase 3 L/N THD"}, "phase_1_current_thd": {"en_US": "Phase 1 Current THD", "it_IT": "Corrente Fase 1 THD"}, "phase_2_current_thd": {"en_US": "Phase 2 Current THD", "it_IT": "Corrente Fase 2 THD"}, "phase_3_current_thd": {"en_US": "Phase 3 Current THD", "it_IT": "Corrente Fase 3 THD"}, "average_line_to_neutral_volts_thd": {"en_US": "Average Line to Neutral Volts THD", "it_IT": "Volt medi da Linea a Neutro THD"}, "average_line_current_thd": {"en_US": "Average Line Current THD", "it_IT": "Corrente media Linea THD"}, "phase_1_current_demand": {"en_US": "Phase 1 Current Demand", "it_IT": "Corrente Richesta Fase 1"}, "phase_2_current_demand": {"en_US": "Phase 2 Current Demand", "it_IT": "Corrente Richesta Fase 2"}, "phase_3_current_demand": {"en_US": "Phase 3 Current Demand", "it_IT": "Corrente Richesta Fase 3"}, "maximum_phase_1_current_demand": {"en_US": "Maximum Phase 1 Current Demand", "it_IT": "Corrente Richesta Massima Fase 1"}, "maximum_phase_2_current_demand": {"en_US": "Maximum Phase 2 Current Demand", "it_IT": "Corrente Richesta Massima Fase 2"}, "maximum_phase_3_current_demand": {"en_US": "Maximum Phase 3 Current Demand", "it_IT": "Corrente Richesta Massima Fase 3"}, "line_1_to_line_2_volts_thd": {"en_US": "Line 1 to Line 2 Volts THD", "it_IT": "Volt Linea 1 a Linea 2 THD"}, "line_2_to_line_3_volts_thd": {"en_US": "Line 2 to Line 3 Volts THD", "it_IT": "Volt Linea 2 a Linea 3 THD"}, "line_3_to_line_1_volts_thd": {"en_US": "Line 3 to Line 1 Volts THD", "it_IT": "Volt Linea 3 a Linea 1 THD"}, "average_line_to_line_volts_thd": {"en_US": "Average Line to Line Volts THD", "it_IT": "Volt medi da Linea a Linea THD"}, "total_active_energy": {"en_US": "Total Active Energy", "it_IT": "Energia Totale Attiva"}, "total_reactive_energy": {"en_US": "Total Reactive Energy", "it_IT": "Energia Totale Reattiva"}, "phase_1_import_active_energy": {"en_US": "Phase 1 Import Active Energy", "it_IT": "Energia Attiva importata Fase 1"}, "phase_2_import_active_energy": {"en_US": "Phase 2 Import Active Energy", "it_IT": "Energia Attiva importata Fase 2"}, "phase_3_import_active_energy": {"en_US": "Phase 3 Import Active Energy", "it_IT": "Energia Attiva importata Fase 3"}, "phase_1_import_reactive_energy": {"en_US": "Phase 1 Import Reactive Energy", "it_IT": "Energia Reattiva importata Fase 1"}, "phase_2_import_reactive_energy": {"en_US": "Phase 2 Import Reactive Energy", "it_IT": "Energia Reattiva importata Fase 2"}, "phase_3_import_reactive_energy": {"en_US": "Phase 3 Import Reactive Energy", "it_IT": "Energia Reattiva importata Fase 3"}, "pressure": {"en_US": "Atmospheric Pressure", "it_IT": "Pressione Atmosferica"}, "gas_resistance": {"en_US": "Gas Resistance", "it_IT": "Resistenza Gas"}, "battery": {"en_US": "Battery Level", "it_IT": "Tensione Batteria"}, "humidity": {"en_US": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Umidità"}, "abbandona_rete": {"en_US": "Network leave", "it_IT": "Abbandona rete"}, "abilita_connessione": {"en_US": "Enable zigbee connection", "it_IT": "Abilita connessione zigbee"}, "abilita_programma_settimanale": {"en_US": "Weekly program", "it_IT": "<PERSON><PERSON>"}, "accendi": {"en_US": "Send ON Command", "it_IT": "Invia Comando ON"}, "PM1_0": {"en_US": "PM1.0", "it_IT": "PM1.0"}, "PM2_5": {"en_US": "PM2.5", "it_IT": "PM2.5"}, "accensione": {"en_US": "Status", "it_IT": "Stato"}, "activation_type": {"en_US": "Activation Type", "it_IT": "Activation Type"}, "actual_energy_100wh": {"en_US": "Energy (100Wh)", "it_IT": "Energia (100Wh)"}, "actual_energy_hca": {"en_US": "Energy (HCA)", "it_IT": "Energia (HCA)"}, "actual_reset_date": {"en_US": "Reset Date", "it_IT": "Data Reset"}, "actual_volume_m3": {"en_US": "Volume (m³)", "it_IT": "Volume (m³)"}, "ActualCounter": {"en_US": "Actual Counter", "it_IT": "Actual Counter"}, "ActualSeriesTime": {"en_US": "Actual Series Time", "it_IT": "Actual Series Time"}, "actuator_enabled": {"en_US": "Actuator Enabled", "it_IT": "Attuatore Abilitato"}, "allarme_centrale": {"en_US": "Central Alarm", "it_IT": "Allarme Centrale"}, "altitudine": {"en_US": "Altitude", "it_IT": "Altit<PERSON><PERSON>"}, "AppEUI": {"en_US": "AppEUI", "it_IT": "AppEUI"}, "AppKey": {"en_US": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "<PERSON><PERSON><PERSON><PERSON>"}, "AppSKey": {"en_US": "Application Security Key", "it_IT": "Chiave Sicurezza Applicazione"}, "attivazione": {"en_US": "Activation", "it_IT": "Attivazione"}, "auto_test_mode": {"en_US": "Auto Test Mode", "it_IT": "Modalità Auto Test"}, "auto_test_result": {"en_US": "Auto Test Result", "it_IT": "Risultato Auto Test"}, "average_current": {"en_US": "Average Current", "it_IT": "Corrente Media"}, "average_power": {"en_US": "Average Power", "it_IT": "Potenza Media"}, "average_time": {"en_US": "Average Time", "it_IT": "Tempo Medio"}, "average_voltage": {"en_US": "Average Voltage", "it_IT": "Voltaggio Medio"}, "AveragePartTime": {"en_US": "Average Part Time", "it_IT": "Average Part Time"}, "Bagno": {"en_US": "Bathroom", "it_IT": "Bagno"}, "batteria_scarica": {"en_US": "Low battery", "it_IT": "<PERSON><PERSON><PERSON> scarica"}, "battery_charging_current": {"en_US": "Battery Charging Current", "it_IT": "Corrente Carica Batteria"}, "battery_defect": {"en_US": "Battery Defect", "it_IT": "Batteria Difettosa"}, "battery_life": {"en_US": "Battery Life", "it_IT": "Stato Batteria"}, "battery_voltage": {"en_US": "Battery Voltage", "it_IT": "Voltaggio Batteria"}, "BendingCycleCounter": {"en_US": "Bending Cycle Counter", "it_IT": "Bending Cycle Counter"}, "BendProcessCount": {"en_US": "Bend Process Count", "it_IT": "Contatore Processo Piegatura"}, "ble_user_id": {"en_US": "UserID BLE", "it_IT": "UserID BLE"}, "bsp_version": {"en_US": "OS version", "it_IT": "Versione SO"}, "camera_1": {"en_US": "Room 1", "it_IT": "Camera 1"}, "camera_2": {"en_US": "Room 2", "it_IT": "Camera 2"}, "camera_3": {"en_US": "Room 3", "it_IT": "Camera 3"}, "camera_4": {"en_US": "Room 4", "it_IT": "Camera 4"}, "carica_batteria": {"en_US": "Battery level", "it_IT": "<PERSON>llo batteria"}, "cct_marciapiede": {"en_US": "Sidewalk CCT", "it_IT": "CCT <PERSON>de"}, "cct_strada": {"en_US": "Road CCT", "it_IT": "CCT Strada"}, "channels": {"en_US": "Channels", "it_IT": "Canali"}, "chiamata": {"en_US": "Call", "it_IT": "<PERSON><PERSON><PERSON>"}, "class": {"en_US": "Class", "it_IT": "Classe"}, "co_simulated": {"en_US": "Simulated CO", "it_IT": "CO Simulato"}, "co2": {"en_US": "CO2", "it_IT": "CO2"}, "co2_alarm": {"en_US": "CO2 Alarm", "it_IT": "Allarme CO2"}, "co2_level": {"en_US": "CO2 Level", "it_IT": "Livello CO2"}, "co2_state": {"en_US": "C02 State", "it_IT": "Stato C02"}, "codice_arm": {"en_US": "Arm Code", "it_IT": "Codice Arm"}, "colors": {"en_US": "Color", "it_IT": "Colore"}, "Comment": {"en_US": "Comment", "it_IT": "Commento"}, "concentration": {"en_US": "Concentration", "it_IT": "Concentrazione"}, "connected_device": {"en_US": "Connected Device", "it_IT": "Dispositivo Connesso"}, "connettivita": {"en_US": "Connectivity", "it_IT": "Connettività"}, "converted_current": {"en_US": "Current", "it_IT": "<PERSON><PERSON><PERSON>"}, "converted_voltage": {"en_US": "Converted Voltage", "it_IT": "Voltaggio Convertito"}, "coord_type": {"en_US": "Coordinator Type", "it_IT": "Tipo Cordinatore"}, "coord_upgradable": {"en_US": "Coordinator Upgradable", "it_IT": "Cordinatore Aggiornabile"}, "corrente": {"en_US": "Current", "it_IT": "<PERSON><PERSON><PERSON>"}, "current_report_frequency": {"en_US": "Device Report Frequency", "it_IT": "Frequenza Report Dispositivo"}, "CurrentBendProcess": {"en_US": "Current Bend Process", "it_IT": "Current Bend Process"}, "day_install": {"en_US": "Days Since Installation", "it_IT": "<PERSON><PERSON><PERSON> installazione"}, "debugParameter": {"en_US": "Debug Parameter", "it_IT": "Parametro di Debug"}, "DevAddr": {"en_US": "<PERSON><PERSON> Address", "it_IT": "Indirizzo Dispositivo"}, "device_id": {"en_US": "Device ID", "it_IT": "Device ID"}, "direzione_vento": {"en_US": "Wind Direction", "it_IT": "Dizezione del vento"}, "display_message": {"en_US": "Display Message", "it_IT": "Messaggio visualizzato"}, "dispositivi": {"en_US": "Devices", "it_IT": "Dispositivi"}, "distanza": {"en_US": "Distance", "it_IT": "Distanza"}, "domenica": {"en_US": "Sunday", "it_IT": "Domenica"}, "downlink": {"en_US": "Downlink", "it_IT": "Downlink"}, "duration_test_result": {"en_US": "Duration Test Result", "it_IT": "Duration Test Result"}, "dust_signal": {"en_US": "PM10", "it_IT": "PM10"}, "dustit_state": {"en_US": "PM10 sensor status", "it_IT": "Stato sensore PM10"}, "email": {"en_US": "Email", "it_IT": "Email"}, "emergenza": {"en_US": "Emergency", "it_IT": "Emergenza"}, "energy": {"en_US": "Energy", "it_IT": "Energia"}, "error_date": {"en_US": "Error Date", "it_IT": "Data Errore"}, "error_state": {"en_US": "Error State", "it_IT": "Stato"}, "error_type": {"en_US": "Error Type", "it_IT": "Tipo Errore"}, "EstimatedPartTime": {"en_US": "Estimated Part Time", "it_IT": "Estimated Part Time"}, "fattore_potenza": {"en_US": "Power Factor", "it_IT": "Fattore Di Potenza"}, "fCntDown": {"en_US": "Frame Counter Down", "it_IT": "Frame Counter Down"}, "fCntUp": {"en_US": "Frame Counter Up", "it_IT": "Frame Counter Up"}, "figli": {"en_US": "Sons", "it_IT": "<PERSON><PERSON>"}, "firmware_version": {"en_US": "Zigbee firmware version", "it_IT": "Versione firmware zigbee"}, "tpu_firmware_version": {"en_US": "TPU firmware version", "it_IT": "Versione firmware TPU"}, "first_converted_current": {"en_US": "Current A", "it_IT": "Corrente A"}, "first_current": {"en_US": "Raw Current A", "it_IT": "Corrente Grezza A"}, "first_multiplier": {"en_US": "Multplier A", "it_IT": "Moltiplicatore A"}, "flusso_marciapiede": {"en_US": "Sidewalk Stream", "it_IT": "<PERSON><PERSON><PERSON>"}, "flusso_strada": {"en_US": "Road Stream", "it_IT": "<PERSON><PERSON><PERSON>"}, "frequenza_campionamento": {"en_US": "Sampling Rate", "it_IT": "Frequenza Campionamento"}, "frequenza_ping": {"en_US": "Frequency", "it_IT": "Frequenza"}, "frequenza_report": {"en_US": "Report Frequency", "it_IT": "Frequenza Report"}, "frequenza_report_c": {"en_US": "Report Frequency C", "it_IT": "Frequenza Report C"}, "frequenza_report_v": {"en_US": "Report Frequency V", "it_IT": "Frequenza Report V"}, "frequenza_report_v2": {"en_US": "Report Frequency V2", "it_IT": "Frequenza Report V2"}, "gas_code": {"en_US": "Gas Code", "it_IT": "Codice Gas"}, "giovedi": {"en_US": "Thursday", "it_IT": "<PERSON><PERSON><PERSON><PERSON>"}, "identifica": {"en_US": "Indentify", "it_IT": "Identifica"}, "illuminance": {"en_US": "Illuminance", "it_IT": "Luminosità"}, "indirizzo_ip": {"en_US": "IP Address", "it_IT": "Indirizzo IP"}, "indoor_air_quality_level": {"en_US": "Air quality level", "it_IT": "<PERSON><PERSON> qualità aria"}, "indoor_air_quality_state": {"en_US": "Air quality state", "it_IT": "Stato qualità aria"}, "intensita_blu": {"en_US": "Blue Intensity", "it_IT": "Intensità Blu"}, "intensita_pioggia": {"en_US": "Rain Intensity", "it_IT": "Intensità di pioggia"}, "intensita_rosso": {"en_US": "Red Intensity", "it_IT": "Intensità Rosso"}, "intensita_verde": {"en_US": "Green Intensity", "it_IT": "Intensità Verde"}, "intensity": {"en_US": "Intensity", "it_IT": "Intensità"}, "internal_temperature": {"en_US": "Internal Temperature", "it_IT": "Temperatura Interna"}, "ip": {"en_US": "IP", "it_IT": "Indirizzo IP"}, "keepalive": {"en_US": "Keepalive", "it_IT": "Keepalive"}, "last_message": {"en_US": "Last Message", "it_IT": "<PERSON><PERSON><PERSON>"}, "latitude": {"en_US": "Latitude", "it_IT": "Latitudine"}, "latitudine": {"en_US": "Latitude", "it_IT": "Latitudine"}, "led_current": {"en_US": "Led Current", "it_IT": "<PERSON><PERSON><PERSON> "}, "led_voltage": {"en_US": "Led Voltage", "it_IT": "Voltaggio Led"}, "level_dim": {"en_US": "Level Dim (%)", "it_IT": "<PERSON><PERSON> (%)"}, "level_max": {"en_US": "Level Max (%)", "it_IT": "<PERSON><PERSON> (%)"}, "lightcube_command": {"en_US": "Lightcube Command", "it_IT": "<PERSON><PERSON><PERSON>"}, "lista_attivazioni": {"en_US": "Activations", "it_IT": "Attivazioni"}, "lista_attuatori": {"en_US": "Actuators", "it_IT": "Attuator<PERSON>"}, "lista_attuatori_discesa": {"en_US": "Actuators Downhill", "it_IT": "Attuatori Discesa"}, "lista_attuatori_salita": {"en_US": "Actuators Climb", "it_IT": "Attuatori Salita"}, "lista_sensori": {"en_US": "Devices List", "it_IT": "<PERSON><PERSON>"}, "lista_sensori_fc_discesa": {"en_US": "Limit <PERSON>", "it_IT": "Fine Corsa Discesa"}, "lista_sensori_fc_salita": {"en_US": "<PERSON><PERSON>", "it_IT": "Fine Corsa Salita"}, "livello": {"en_US": "Level Status", "it_IT": "<PERSON><PERSON>"}, "lock_state": {"en_US": "Lock State", "it_IT": "Stato Serratura"}, "lock_type": {"en_US": "Lock Type", "it_IT": "Tip<PERSON>"}, "longitude": {"en_US": "Longitude", "it_IT": "<PERSON><PERSON><PERSON><PERSON>"}, "longitudine": {"en_US": "Longitude", "it_IT": "<PERSON><PERSON><PERSON><PERSON>"}, "lost_accumulation_time": {"en_US": "Lost Accumulation Time", "it_IT": "Tempo Perdita Accumulazione"}, "luminosita": {"en_US": "Brightness", "it_IT": "Luminosità"}, "luminosita_attuale": {"en_US": "Current Level", "it_IT": "<PERSON><PERSON> Attuale"}, "lunedi": {"en_US": "Monday", "it_IT": "Lunedi"}, "mac_address": {"en_US": "<PERSON>dress", "it_IT": "<PERSON>dress"}, "MachineName": {"en_US": "Machine Name", "it_IT": "Nome <PERSON>"}, "MachineState": {"en_US": "Machine State", "it_IT": "Stato Macchina"}, "MachineType": {"en_US": "Machine Type", "it_IT": "<PERSON><PERSON><PERSON>"}, "manual_auto_test": {"en_US": "Manual Auto Test", "it_IT": "Auto Test Manuale"}, "manual_duration_test": {"en_US": "Manual Duration Test", "it_IT": "Manual Duration Test"}, "martedi": {"en_US": "Tuesday", "it_IT": "<PERSON><PERSON><PERSON>"}, "maximum_value": {"en_US": "Maximum Value", "it_IT": "<PERSON><PERSON>"}, "mercoledi": {"en_US": "Wednesday", "it_IT": "<PERSON><PERSON><PERSON><PERSON>"}, "minimum_value": {"en_US": "Minimum Value", "it_IT": "Valore Mini<PERSON>"}, "modalita": {"en_US": "Modality", "it_IT": "Modalità"}, "movimento": {"en_US": "Status", "it_IT": "Stato"}, "NominalCounter": {"en_US": "Nominal Counter", "it_IT": "Contatore No<PERSON>le"}, "NwkSKey": {"en_US": "Network Security Key", "it_IT": "<PERSON><PERSON>"}, "occupancy": {"en_US": "Occupancy", "it_IT": "Presenza"}, "opening": {"en_US": "Opening", "it_IT": "Apertura"}, "OperatingMinutesCounter": {"en_US": "Operating Minutes Counter", "it_IT": "Operating Minutes Counter"}, "operation_mode": {"en_US": "Operation Mode", "it_IT": "Modalità Operativa"}, "ota_upgrade_percentage": {"en_US": "Ota Upgrade Percentage", "it_IT": "Percentuale Aggiornamento Ota"}, "ota_upgrade_status": {"en_US": "Ota Upgrade Status", "it_IT": "Stato Aggiornamento Ota"}, "output_power_status": {"en_US": "Output Power Status", "it_IT": "Stato Potenza in Output"}, "ovp": {"en_US": "OVP", "it_IT": "OVP"}, "panico": {"en_US": "Panic", "it_IT": "Panico"}, "Partizione 1": {"en_US": "Partition 1", "it_IT": "Partizione 1"}, "Partizione 2": {"en_US": "Partition 2", "it_IT": "Partizione 2"}, "Partizione 3": {"en_US": "Partition 3", "it_IT": "Partizione 3"}, "PartName": {"en_US": "Part Name", "it_IT": "Nome Parte"}, "pdr1": {"en_US": "PDR1", "it_IT": "PDR1"}, "pdr2": {"en_US": "PDR2", "it_IT": "PDR2"}, "pdr3": {"en_US": "PDR3", "it_IT": "PDR3"}, "pdt1": {"en_US": "PDT1", "it_IT": "PDT1"}, "pdt2": {"en_US": "PDT2", "it_IT": "PDT2"}, "pdt3": {"en_US": "PDT3", "it_IT": "PDT3"}, "Perimetrale": {"en_US": "Perimetral", "it_IT": "Perimetrale"}, "pm10_simulated": {"en_US": "Simulated PM10", "it_IT": "PM10 Simulato"}, "porta": {"en_US": "Port", "it_IT": "Porta"}, "potenza": {"en_US": "Power", "it_IT": "Potenza"}, "Potenza": {"en_US": "Power", "it_IT": "Potenza Elettrica"}, "power_accumulation": {"en_US": "Power Accumulation", "it_IT": "Accum<PERSON>"}, "presenza": {"en_US": "Status", "it_IT": "Stato"}, "presenza_acqua": {"en_US": "Water presence", "it_IT": "Presenza acqua"}, "presenza_fumo": {"en_US": "Smoke presence", "it_IT": "Presenza fumo"}, "presenza_monossido": {"en_US": "CO", "it_IT": "CO"}, "qualita_fix": {"en_US": "Fix Quality", "it_IT": "Qualità fix"}, "raggio": {"en_US": "Range", "it_IT": "Intervallo"}, "first_flame": {"en_US": "First Flame", "it_IT": "Prima Fiamma"}, "second_flame": {"en_US": "Second Flame", "it_IT": "<PERSON><PERSON>"}, "burner_ignition": {"en_US": "Burner Ignition", "it_IT": "Accensione Bruciatore"}, "locked_state": {"en_US": "Locked State", "it_IT": "Stato di Blocco"}, "operating_state": {"en_US": "Operating State", "it_IT": "Stato di Funzionamento"}, "registra_movimento": {"en_US": "Record Movement", "it_IT": "Registra Movimento"}, "RemainingCounter": {"en_US": "Remaining Counter", "it_IT": "Remaining Counter"}, "RemainingSeriesTime": {"en_US": "Remaining Series Time", "it_IT": "Remaining Series Time"}, "reset_energy": {"en_US": "Reset Energy", "it_IT": "Reset Energy"}, "rfid_id": {"en_US": "RFID Id", "it_IT": "RFID Id"}, "ricerca": {"en_US": "<PERSON><PERSON>", "it_IT": "Scansione"}, "sabato": {"en_US": "Saturday", "it_IT": "Sabato"}, "scansione": {"en_US": "<PERSON>an <PERSON>", "it_IT": "Tempo Scansione"}, "second_converted_current": {"en_US": "Current B", "it_IT": "Corrente B"}, "second_current": {"en_US": "Raw Current B", "it_IT": "Corrente Grezza B"}, "second_multiplier": {"en_US": "Multiplier 3", "it_IT": "Moltiplicatore B"}, "segnalazione": {"en_US": "Reporting", "it_IT": "Segnalazione"}, "set_led_current": {"en_US": "Set  Led Current", "it_IT": "Setta Corrente Led"}, "set_point_attuale": {"en_US": "Set point", "it_IT": "Soglia"}, "sidewalk_on_off": {"en_US": "Sidewalk light", "it_IT": "<PERSON>"}, "signal_level": {"en_US": "Triggered status", "it_IT": "Stato attivazione"}, "signaling_on_off": {"en_US": "Signaling light", "it_IT": "Luce di segnalazione"}, "smoke": {"en_US": "Smoke presence", "it_IT": "Presenza fumo"}, "sms": {"en_US": "SMS", "it_IT": "SMS"}, "soglia_isteresi": {"en_US": "Hysteresis", "it_IT": "<PERSON><PERSON><PERSON>"}, "soil_moisture": {"en_US": "Soil Moisture", "it_IT": "Umidità Terreno"}, "state": {"en_US": "State", "it_IT": "Stato"}, "stato": {"en_US": "Status", "it_IT": "Stato"}, "stato_allarme": {"en_US": "Alarm", "it_IT": "Allarme"}, "stato_attivazione": {"en_US": "Activation", "it_IT": "Attivazione"}, "stato_motore": {"en_US": "Engine", "it_IT": "Motore"}, "status": {"en_US": "Status", "it_IT": "Stato"}, "stored_energy_100wh": {"en_US": "Archived Energy (100Wh) ", "it_IT": "Energia (100Wh) anno precedente "}, "stored_energy_hca": {"en_US": "Archived Energy (HCA) ", "it_IT": "Energia (HCA)  anno precedente "}, "stored_reset_date": {"en_US": "Reset Date", "it_IT": "Data Reset"}, "stored_volume_m3": {"en_US": "Archived Volume (m³)", "it_IT": "Volume (m³)  anno precedente "}, "street_on_off": {"en_US": "Street light", "it_IT": "Luce strada"}, "tamper": {"en_US": "<PERSON><PERSON>", "it_IT": "Manomissione"}, "temperatura": {"en_US": "Temperature", "it_IT": "Temperatura"}, "temperature": {"en_US": "Temperature", "it_IT": "Temperatura"}, "temperature_0": {"en_US": "Flow Temperature", "it_IT": "Temperatura di Mandata"}, "temperature_1": {"en_US": "Outside Temperature", "it_IT": "Temperatura Esterna"}, "temperature_2": {"en_US": "Boiler Temperature", "it_IT": "Temperatura Caldaia"}, "tensione": {"en_US": "Voltage", "it_IT": "Tensione"}, "tensione_ac": {"en_US": "Voltage AC", "it_IT": "Tensione AC"}, "third_converted_current": {"en_US": "Current C", "it_IT": "Corrente C"}, "third_current": {"en_US": "Raw Current C", "it_IT": "Corrente Grezza C"}, "third_multiplier": {"en_US": "Multplier C", "it_IT": "Moltiplicatore C"}, "time_dim": {"en_US": "Time Level Dimming (in seconds)", "it_IT": "<PERSON><PERSON> (in secondi)"}, "time_max": {"en_US": "Time Level Max (in seconds)", "it_IT": "<PERSON><PERSON> (in secondi)"}, "tipo_dispositivo": {"en_US": "Device Type", "it_IT": "Tipo Dispositivo"}, "TotalSeriesTime": {"en_US": "Total Series Time", "it_IT": "Total Series Time"}, "umidita": {"en_US": "<PERSON><PERSON><PERSON><PERSON>", "it_IT": "Umidità"}, "unlock_door": {"en_US": "Unlock Door Deprecated", "it_IT": "Apertura Porta Deprecata"}, "unlock_door_custom": {"en_US": "Unlock Door", "it_IT": "Apertura Porta"}, "uplink": {"en_US": "Uplink", "it_IT": "Uplink"}, "url": {"en_US": "View", "it_IT": "Vista"}, "vca_avg_speed": {"en_US": "Vca Average Speed", "it_IT": "Vca Velocità Media"}, "vca_count_people_in": {"en_US": "Count people in", "it_IT": "<PERSON><PERSON><PERSON> persone in"}, "vca_count_people_out": {"en_US": "Count people out", "it_IT": "<PERSON><PERSON><PERSON> persone out"}, "vca_count_veichles": {"en_US": "Vca Count Vehicle", "it_IT": "Vca Conteggio Veicoli"}, "vca_max_speed": {"en_US": "Max speed", "it_IT": "Velocità massima"}, "velocita_vento": {"en_US": "Wind speed", "it_IT": "Velocità del vento"}, "venerdi": {"en_US": "Friday", "it_IT": "<PERSON><PERSON><PERSON>"}, "visualizza_gps": {"en_US": "View Location", "it_IT": "Visualizza GPS"}, "zigbee_attribute": {"en_US": "ZigBee Attribute", "it_IT": "Attributo ZigBee"}, "zigbee_cluster": {"en_US": "ZigBee Cluster", "it_IT": "Cluster ZigBee"}, "zigbee_value": {"en_US": "ZigBee Value", "it_IT": "<PERSON><PERSON>"}, "Zona giorno": {"en_US": "Living Area", "it_IT": "Zona Giorno"}, "Zona notte": {"en_US": "Sleeping Area", "it_IT": "Zona Notte"}, "zone_controllate": {"en_US": "Monitored Zones", "it_IT": "Zone Controllate"}, "scene_selected": {"en_US": "Selected Scene", "it_IT": "Scena Selezionata"}, "on_off": {"en_US": "State", "it_IT": "Stato"}, "level": {"en_US": "Level", "it_IT": "<PERSON><PERSON>"}, "sensing_level": {"en_US": "Sensing Level", "it_IT": "Sensibilità sensore"}, "vibration": {"en_US": "Shock Vibration", "it_IT": "Vibrazione d'urto"}, "voc": {"en_US": "Voc", "it_IT": "Voc"}, "setpoint": {"en_US": "Setpoint", "it_IT": "Setpoint"}, "local_temperature_calibration": {"en_US": "Temperature offset", "it_IT": "Offset temperatura"}, "system_mode": {"en_US": "System mode", "it_IT": "Modalità sistema"}, "control_sequence_of_operation": {"en_US": "Control sequence of operation", "it_IT": "Sequenza di controllo delle operazioni"}, "remote_control_mode": {"en_US": "Remote control mode", "it_IT": "Modalità di controllo remoto"}, "remote_on_off": {"en_US": "Remote device status", "it_IT": "Stato dispositivo remoto"}, "internal_battery": {"en_US": "Internal battery", "it_IT": "Batteria interna"}, "external_battery": {"en_US": "External battery", "it_IT": "<PERSON><PERSON>ia esterna"}}