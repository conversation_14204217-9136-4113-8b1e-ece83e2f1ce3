{"categories": [{"id": "13", "name": "Gps"}, {"id": "0", "name": "Zigbee"}, {"id": "8", "name": "PT100"}, {"id": "11", "name": "Modbus"}, {"id": "1", "name": "GSM"}, {"id": "6", "name": "ONVIF"}, {"id": "5", "name": "<PERSON><PERSON>"}, {"id": "4", "name": "<PERSON><PERSON><PERSON>"}, {"id": "3", "name": "<PERSON><PERSON><PERSON>"}, {"id": "16", "name": "Opcua"}, {"id": "17", "name": "Wmbus"}, {"id": "7", "name": "Thermostat"}, {"id": "2", "name": "SMTP"}, {"id": "10", "name": "Shutter"}, {"id": "9", "name": "GPIO"}, {"id": "15", "name": "<PERSON><PERSON><PERSON>"}, {"id": "12", "name": "Time Programmer"}], "endpoints": [{"cardinality": {"max": "1"}, "category": "0", "default_parameter": "abilita_connessione", "description": "Used to create a graphical element referred to the BOXIO device", "image": "<ns0:svg xmlns:ns0=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\">\n          <ns0:text x=\"0.041984558\" y=\"38.530727\" style=\"font-style:normal;font-variant:normal;font-weight:bold;font-stretch:normal;font-size:17.37668037px;line-height:125%;font-family:Arial;-inkscape-font-specification:Arial Bold;letter-spacing:0px;word-spacing:0px;fill:#000000;fill-opacity:1;stroke:none\">\n            <ns0:tspan>BOX-IO</ns0:tspan>\n          </ns0:text>\n        </ns0:svg>\n      ", "mapping": {"description": "Zigbee IEEE Address", "input_mask": "HHHHHHHHHHHHHHHH", "validation_regex": "([a-fA-F0-9]{2}){8}", "visualization_name": "IEEE Address"}, "parameters": [{"data_type": "STRING", "default_log_type": "TEXT", "description": "Show the Boxio OS version", "input_record": false, "name": "bsp_version"}, {"data_type": "STRING", "default_log_type": "NONE", "description": "The MAC address of the Boxio gateway ethernet interface", "input_record": true, "name": "mac_address", "validation_regex": "([a-fA-F0-9]{2}:){5}[a-fA-F0-9]{2}"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "Enable new Zigbee devices to join the Boxio network", "input_record": false, "name": "abilita_connessione", "operation": {"name": "switch"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "Show the Zigbee Coordinator's firmware version", "input_record": false, "name": "firmware_version"}, {"data_type": "STRING", "default_log_type": "NONE", "description": "Show the Zigbee Coordinator's device type", "input_record": false, "name": "coord_type"}, {"data_type": "BOOL", "default_log_type": "NONE", "description": "Show if the Zigbee Coordinator is upgradable (has the bootloader)", "input_record": false, "name": "coord_upgradable"}, {"data_type": "BOOL", "default_log_type": "NONE", "description": "Connectivity attribute, true if the endpoint is online", "input_record": false, "name": "connettivita"}, {"admitted_log_types": ["NONE", "TEXT"], "data_type": "LIST", "default_log_type": "NONE", "description": "List of Zigbee connected nodes.", "input_record": false, "name": "figli", "visualization_name": "Connected nodes"}], "tag": "Gateway", "tags": ["Gateway"], "visualization_name": "eTensil IoT Gateway", "visualization_type": "BOXIO"}, {"category": "0", "default_parameter": "device_id", "description": "eTensil is a complete range of industrial electric screwdrivers, nutrunner motors and automatic tightening solutions designed entirely by Fiam.", "image": "", "mapping": {"description": "Zigbee IEEE Address", "input_mask": "HHHHHHHHHHHHHHHH", "validation_regex": "([a-fA-F0-9]{2}){8}", "visualization_name": "IEEE Address"}, "parameters": [{"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "identifica il modello della centralina", "enum_vals": ["TPU-C1", "TPU-C3", "TPU-M1", "TPU-A3"], "input_record": false, "name": "device_id"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "versione del firmware tpu", "input_record": false, "name": "tpu_firmware_version"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "matricola avvitatore utilizzato", "input_record": false, "name": "serial_number"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "modello avvitatore utilizzato", "input_record": false, "name": "tool_code"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "Versione hardware et adapter", "input_record": false, "name": "et_adapter_hardware_version"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "Versione firmware modulo stm32 et adapter", "input_record": false, "name": "et_adapter_stm32_firmware_version"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "Versione firmware modulo ebyte et adapter", "input_record": false, "name": "et_adapter_ebyte_firmware_version"}, {"data_type": "BOOL", "default_log_type": "NONE", "description": "Connectivity attribute, true if the endpoint is online", "input_record": false, "name": "connettivita"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "Send command to ask the device to leave the zigbee network", "input_record": false, "name": "abbandona_rete", "operation": {"name": "button"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "stato di sincronizzazione dell'endpoint", "enum_vals": ["NOT_CONFIGURED", "CONFIGURING", "CONFIGURED"], "input_record": false, "name": "configuration_state"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "genera una richiesta di sincronizzazione dello stato del dispositivo", "input_record": false, "name": "sync", "operation": {"name": "trigger"}}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "stato di accensione/spegnimento di eTensil", "input_record": false, "name": "state"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "opzioni centralina", "input_record": false, "name": "options", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 1", "input_record": false, "name": "program_1", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 2", "input_record": false, "name": "program_2", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 3", "input_record": false, "name": "program_3", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 4", "input_record": false, "name": "program_4", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 5", "input_record": false, "name": "program_5", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 6", "input_record": false, "name": "program_6", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 7", "input_record": false, "name": "program_7", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "programma n° 8", "input_record": false, "name": "program_8", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "configurazione degli I/O", "input_record": false, "name": "io_conf", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "sequenza", "input_record": false, "name": "sequence", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "timestamp dell’ultimo avvio della centralina", "input_record": false, "name": "evt_start_time"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "timestamp dell’ultimo spegnimento della centralina", "input_record": false, "name": "evt_shutdown_time"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "timestamp dell’ultimo reset del programma di avvitatura", "input_record": false, "name": "evt_cycle_reset_time"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "contenitore del risultato di avvitatura", "input_record": false, "name": "evt_screwing_result"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "stato degli 8 PIN di output", "input_record": false, "name": "io_out"}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "parametro degli 8 PIN di input", "input_record": false, "name": "io_in", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "contenitore della porzione di memoria da scrivere", "input_record": false, "name": "memory_changes", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro opzioni", "enum_vals": ["EN", "IT", "FR", "ES", "DE"], "input_record": false, "name": "m_lingua", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro opzioni", "enum_vals": ["Nm", "Lb/in", "Kgf.cm"], "input_record": false, "name": "m_unita_mis", "operation": {"name": "update"}}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro opzioni", "input_record": false, "name": "m_buzzer", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro opzioni", "enum_vals": ["9600", "19200", "38400", "57600"], "input_record": false, "name": "m_RS232_speed", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro opzioni", "input_record": false, "max_val": "20", "min_val": "1", "name": "m_scala_led", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro opzioni", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luminosita_led", "operation": {"name": "update"}, "step": "10"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1.5", "description": "parametro opzioni", "input_record": false, "max_val": "9.9", "min_val": "0.0", "name": "m_delay_bloccapezzo", "operation": {"name": "update"}, "step": "0.1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro opzioni", "input_record": false, "name": "m_log_sn", "operation": {"name": "update"}}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro opzioni", "input_record": false, "name": "m_log_orologio", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro opzioni", "enum_vals": ["NO", "L1", "L2"], "input_record": false, "name": "m_password_livello", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "parametro opzioni", "input_record": false, "name": "m_password", "operation": {"name": "update"}}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro opzioni", "input_record": false, "name": "m_inverti_CR_LF", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°1", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p1", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°1", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p1", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°1", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p1", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p1", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°1", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p1", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°1", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p1", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p1", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p1", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p1", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p1", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p1", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°1", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p1", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p1", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°1", "input_record": false, "name": "m_scatto_formatura_p1", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p1", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°1", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°1", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°1", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p1", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°1", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p1", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°1", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p1", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°1", "input_record": false, "name": "m_svitatura_consentita_p1", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°1", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p1", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°1", "input_record": false, "name": "m_avvitatura_sinistra_p1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°1", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p1", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°1", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p1", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°2", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p2", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°2", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p2", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°2", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p2", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p2", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°2", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p2", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°2", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p2", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p2", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p2", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p2", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p2", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p2", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°2", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p2", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p2", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°2", "input_record": false, "name": "m_scatto_formatura_p2", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p2", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°2", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°2", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°2", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p2", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°2", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p2", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°2", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p2", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°2", "input_record": false, "name": "m_svitatura_consentita_p2", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°2", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p2", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°2", "input_record": false, "name": "m_avvitatura_sinistra_p2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°2", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p2", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°2", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p2", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°3", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p3", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°3", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p3", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°3", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p3", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p3", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°3", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p3", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°3", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p3", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p3", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p3", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p3", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p3", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p3", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°3", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p3", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p3", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°3", "input_record": false, "name": "m_scatto_formatura_p3", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p3", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°3", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p3", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°3", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p3", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°3", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p3", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°3", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p3", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°3", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p3", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°3", "input_record": false, "name": "m_svitatura_consentita_p3", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°3", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p3", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°3", "input_record": false, "name": "m_avvitatura_sinistra_p3", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°3", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p3", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°3", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p3", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°4", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p4", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°4", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p4", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°4", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p4", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p4", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°4", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p4", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°4", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p4", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p4", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p4", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p4", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p4", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p4", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°4", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p4", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p4", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°4", "input_record": false, "name": "m_scatto_formatura_p4", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p4", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°4", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p4", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°4", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p4", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°4", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p4", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°4", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p4", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°4", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p4", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°4", "input_record": false, "name": "m_svitatura_consentita_p4", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°4", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p4", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°4", "input_record": false, "name": "m_avvitatura_sinistra_p4", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°4", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p4", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°4", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p4", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°5", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p5", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°5", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p5", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°5", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p5", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p5", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°5", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p5", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°5", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p5", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p5", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p5", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p5", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p5", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p5", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°5", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p5", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p5", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°5", "input_record": false, "name": "m_scatto_formatura_p5", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p5", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°5", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p5", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°5", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p5", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°5", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p5", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°5", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p5", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°5", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p5", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°5", "input_record": false, "name": "m_svitatura_consentita_p5", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°5", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p5", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°5", "input_record": false, "name": "m_avvitatura_sinistra_p5", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°5", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p5", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°5", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p5", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°6", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p6", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°6", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p6", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°6", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p6", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p6", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°6", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p6", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°6", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p6", "operation": {"name": "update"}, "step": "0.1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p6", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p6", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p6", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p6", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p6", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°6", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p6", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p6", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°6", "input_record": false, "name": "m_scatto_formatura_p6", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p6", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°6", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p6", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°6", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p6", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°6", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p6", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°6", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p6", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°6", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p6", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°6", "input_record": false, "name": "m_svitatura_consentita_p6", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°6", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p6", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°6", "input_record": false, "name": "m_avvitatura_sinistra_p6", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°6", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p6", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°6", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p6", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°7", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p7", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°7", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p7", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°7", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p7", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p7", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°7", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p7", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°7", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p7", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p7", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p7", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p7", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p7", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p7", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°7", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p7", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p7", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°7", "input_record": false, "name": "m_scatto_formatura_p7", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p7", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°7", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p7", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°7", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p7", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°7", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p7", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°7", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p7", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°7", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p7", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°7", "input_record": false, "name": "m_svitatura_consentita_p7", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°7", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p7", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°7", "input_record": false, "name": "m_avvitatura_sinistra_p7", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°7", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p7", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°7", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p7", "operation": {"name": "update"}, "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "5", "description": "parametro programma n°8", "input_record": false, "max_val": "99", "min_val": "1", "name": "m_viti_p8", "operation": {"name": "update"}, "step": "1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "1", "description": "parametro programma n°8", "input_record": false, "max_val": "9", "min_val": "0.3", "name": "m_coppia_p8", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.05", "description": "parametro programma n°8", "input_record": false, "max_val": "3", "min_val": "0.05", "name": "m_toll_coppia_p8", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "5", "min_val": "-2", "name": "m_k_coppia_p8", "operation": {"name": "update"}, "step": "0.01"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "25", "description": "parametro programma n°8", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_final_speed_p8", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.2", "description": "parametro programma n°8", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_tempo_accelerazione_p8", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_min_p8", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "20", "min_val": "0", "name": "m_tempo_max_p8", "operation": {"name": "update"}, "step": "0.01", "unit": "second"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_min_p8", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_max_p8", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "70", "min_val": "0", "name": "m_soglia_angolo_p8", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°8", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_formatura_p8", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "9995", "min_val": "0", "name": "m_angolo_formatura_p8", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°8", "input_record": false, "name": "m_scatto_formatura_p8", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "99", "min_val": "0", "name": "m_errori_massimi_p8", "operation": {"name": "update"}, "step": "1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°8", "input_record": false, "name": "m_errore_r<PERSON><PERSON><PERSON>_leva_p8", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°8", "enum_vals": ["NO", "ENTER", "ESC", "ALL"], "input_record": false, "name": "m_ENTER_ESC_press_p8", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°8", "enum_vals": ["OFF", "PRE", "POST"], "input_record": false, "name": "m_autosvitatura_p8", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "description": "parametro programma n°8", "input_record": false, "max_val": "9995", "min_val": "30", "name": "m_angolo_autosvit_p8", "operation": {"name": "update"}, "step": "5", "unit": "degree"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0.3", "description": "parametro programma n°8", "input_record": false, "max_val": "2.5", "min_val": "0.2", "name": "m_pausa_autosvit_p8", "operation": {"name": "update"}, "step": "0.1", "unit": "second"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°8", "input_record": false, "name": "m_svitatura_consentita_p8", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°8", "input_record": false, "max_val": "100", "min_val": "15", "name": "m_velocita_svitatura_p8", "operation": {"name": "update"}, "step": "1", "unit": "percent"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro programma n°8", "input_record": false, "name": "m_avvitatura_sinistra_p8", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro programma n°8", "enum_vals": ["LEVER", "PUSH TO START", "P+L", "P+Lr"], "input_record": false, "name": "m_modo_avvio_p8", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "100", "description": "parametro programma n°8", "input_record": false, "max_val": "100", "min_val": "0", "name": "m_luce_frontale_p8", "operation": {"name": "update"}, "step": "1"}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_3", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_4", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_5", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_6", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_7", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Pwr Off", "Reset", "Enter", "Esc", "<PERSON>ighten", "Untighten", "Stop", "Unlock", "LockPart", "Seq act", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_input_8", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_3", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_4", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_5", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_6", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_7", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["Not used", "Ready", "Run", "Untighten", "<PERSON><PERSON>", "Prog end", "Seq End", "<PERSON><PERSON>K", "Runcycle", "Reject", "Stop", "LockPart", "Prog 1", "Prog 2", "Prog 3", "Prog 4", "Prog 5", "Prog 6", "Prog 7", "Prog 8", "Prg bit0", "Prg bit1", "Prg bit2"], "input_record": false, "name": "m_output_8", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "description": "parametro configurazione I/O", "enum_vals": ["<PERSON><PERSON><PERSON>", "Locale"], "input_record": false, "name": "m_gpio_power", "operation": {"name": "update"}}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "1.5", "min_val": "0", "name": "m_retainment_ok", "operation": {"name": "update"}, "step": "0.1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "1.5", "min_val": "0", "name": "m_retainment_nok", "operation": {"name": "update"}, "step": "0.1"}, {"data_type": "FLOAT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "1.5", "min_val": "0", "name": "m_retainment_scarto", "operation": {"name": "update"}, "step": "0.1"}, {"data_type": "BOOL", "default_log_type": "TEXT", "description": "parametro sequenza", "input_record": false, "name": "m_sequenza_attiva", "operation": {"name": "update"}}, {"data_type": "STRING", "default_log_type": "TEXT", "description": "parametro sequenza", "input_record": false, "name": "m_sequenza", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_non_usato", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_spegnimento", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_reset", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_enter", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_esc", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_avvita", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_svita", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_stop", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_sblocco", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_blocca_p", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_seq_attiva", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog3", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog4", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog5", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog6", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog7", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prog8", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prg_bit0", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prg_bit1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "input_prg_bit2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_non_usato", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_pronto", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_rotazione", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_reverse", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_vite_ok", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_fine_prog", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_fine_seq", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_vite_nok", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_ciclo", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_scarto", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_stop", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_blocca_pz", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog2", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog3", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog4", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog5", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog6", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog7", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prog8", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prg_bit0", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prg_bit1", "operation": {"name": "update"}}, {"data_type": "ENUMERATION", "default_log_type": "TEXT", "enum_vals": ["NOT_MAPPED", "NOT_OVERRIDDEN_0", "NOT_OVERRIDDEN_1", "0", "1"], "input_record": false, "name": "output_prg_bit2", "operation": {"name": "update"}}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "16777215", "min_val": "0", "name": "m_viti_fatte", "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "16777215", "min_val": "0", "name": "m_sequenze_fatte", "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "16777215", "min_val": "0", "name": "m_viti_NOK", "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "16777215", "min_val": "0", "name": "m_reset_premuti", "step": "1"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "279620", "min_val": "0", "name": "m_ore_on", "step": "1", "unit": "hour"}, {"data_type": "INT", "default_log_type": "TEXT", "default_value": "0", "input_record": false, "max_val": "16777215", "min_val": "0", "name": "m_spegnimenti_errati", "step": "1"}, {"data_type": "LIST", "default_log_type": "TEXT", "description": "Reports the specific operation error (OperationId|WriteError|CommandCode|ParameterCode)", "input_record": false, "name": "error"}], "visualization_name": "eTensil IoT Adapter", "visualization_type": "ETENSIL"}], "version": "2.0.30-fiam"}