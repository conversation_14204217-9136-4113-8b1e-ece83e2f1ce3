#!/bin/bash
. /etc/profile.d/set-docker-profile.sh
DIR="$(
    cd -- "$(dirname "$0")" >/dev/null 2>&1
    pwd -P
)"
PARENTDIR="$(dirname "$DIR")"
ENV_COPY="/tmp/envNormalized"
cp $PARENTDIR/.env $ENV_COPY
sed 's/"//g' -i $ENV_COPY
sed 's/\(=[[:blank:]]*\)\(.*\)/\1"\2"/' -i $ENV_COPY
source $ENV_COPY .

if [ -f "$ENV_COPY" ]; then
    rm $ENV_COPY
fi

VOLUME_PATH="$PARENTDIR/certs"
SYNC_PATH="$PARENTDIR/$SSL_HOST_VOLUME/server"
CERT_PATH="/etc/letsencrypt/live/$DNS_NAME"

FULLCHAIN="$CERT_PATH/fullchain.pem"
PRIVKEY="$CERT_PATH/privkey.pem"

KEYSTORE="$CERT_PATH/keystore.p12"
if [ -f $KEYSTORE ]; then
    mv $KEYSTORE "$KEYSTORE.bck"
fi

KEYALIAS="boxio"
KEYPWD="$KEYSTORE_PASSWORD"

openssl pkcs12 -export -legacy -name $KEYALIAS -out $KEYSTORE -in $FULLCHAIN -inkey $PRIVKEY -passout pass:$KEYPWD

rsync -aL $CERT_PATH/* $SYNC_PATH/
chmod 655 $SYNC_PATH
chmod 640 $SYNC_PATH/keystore.p12
chmod 640 $SYNC_PATH/privkey.pem
chown -R root:990 $VOLUME_PATH/

cd $PARENTDIR
docker-compose -f $PARENTDIR/docker-compose.yml down
docker-compose -f $PARENTDIR/docker-compose.yml up -d
cd -
exit 0
