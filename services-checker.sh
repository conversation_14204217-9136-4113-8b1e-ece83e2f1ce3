#!/bin/bash
declare -a expected=(`grep "\# persistent:" docker-compose.yml |sed -e 's/[ #]* persistent: //g' |sort`)
declare -a running=(`docker ps --format '{{.Names}}'|sort`)

i=0
j=0
green="\e[92m";
red="\e[91m";
end="\e[0m";

while [ $i -lt ${#expected[@]} ]; do
    if [ "${expected[i]}" != "${running[j]}" ]; then
        printf "$red%-25s KO$end\n" "${expected[i]}"
        i=$((i+1))
    else
        printf "$green%-25s OK$end\n" "${expected[i]}"
        i=$((i+1))
        j=$((j+1))
    fi
done
