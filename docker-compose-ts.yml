version: '3'
services:
      auth-database:
        #image: ${REGISTRY_NAME}/edalab/boxio/cloud/docker/postgres/safe-place:${AUTHDB_VERSION}
        image: timescale/timescaledb-ha:pg16-ts2.13-oss
        #image: timescale/timescaledb-ha:pg15-ts2.13-oss
        #image: timescale/timescaledb-ha:pg15-ts2.11-oss
        # image: timescale/timescaledb-ha:pg12-ts2.11-oss
          #image: timescale/timescaledb-ha:pg12-ts2.0.0-rc4
        #image: timescale/timescaledb-ha:pg12-ts1.7-oss
        #image: ${REGISTRY_NAME}/edalab/boxio/cloud/docker/postgres/safe-place:${AUTHDB_VERSION}
        container_name: auth-database
        environment:
            - POSTGRES_USER=${AUTHDB_USER}
            - POSTGRES_PASSWORD=${AUTHDB_PASSWORD}
        volumes:
          # - ${AUTHDB_HOST_VOLUME}:${AUTHDB_CONTAINER_VOLUME}
          #- ./auth-database/data12:/home/<USER>/pgdata/data
          #        - ./auth-database/data15:/home/<USER>/pgdata/data
           - ./auth-database/data16:/home/<USER>/pgdata/data
        env_file:
            - .env
        expose:
            - 5432
        ports:
            - 5432:5432
        restart: always
