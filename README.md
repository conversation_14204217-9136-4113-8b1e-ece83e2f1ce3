# Boxio docker-compose

## Overview

This repository contains the files to set up a BOXIO environment using [Docker](https://docs.docker.com/get-started/overview/).

The scope of the project is to simplify the installation of the BOXIO software inside a machine

## EDALab policy information

At the moment for each EDALab component that needs to be installed via Docker, we decided to adopt the following approach:

- Create a new repository for the project
- Create a branch for each machine in order to keep track of the machine structure. This is true also for machines having the exact same structure.
- Hide secrets at the repository level. This means that the secrets of the machine won't be stored inside GitLab but will only be available inside the machine .env file.

For this to be true inside this project, we need to not keep track of modifications made for the following files:

- .env => environment, containing passwords and other sensitive information
- etc/\*.ini => environment files for CouchDB

We still need to have a skeleton of the files in order to run the project for the first time inside the machine, so the approach we chose was to have those files inside the repo, saved as \*\_example, and to gitignore their counterpart.

On launching the setup script, the files will be copied in order to have the correct naming convention, and the .env file will be initialized with new passwords automatically

# Requirements on the machine

- python3
- bash

## Procedure description

Clone the repository inside the machine, using the HTTPS method. At the moment this is the best approach, as we won't save any credentials inside the machine.

To be even safer you could generate your authentication token just in time from here [Access token generation](https://git.edalab.it/-/profile/personal_access_tokens) and give to it just read_repository permissions. Then clone the repository:

- git clone https://git.edalab.it/path/to/project

After cloning the project, launch the setup script which will do the following operations:

- Verify if passwords needs to be generated -> generate them
- Verify if ufw (firewall) needs to be installed -> install it
- Fixup configuration files according to the passwords file

## Launch

At this point, review both the docker-compose and env file in order to understand if everything is ready. Modify the passwords if needed (e.g. if
you are working inside a server which is being upgraded to docker environment, you will probably need to setup the old passwords, or maybe not)

When you are read to launch the program, generate another token with read registry permissions [Access token generation](https://git.edalab.it/-/profile/personal_access_tokens), then from the directory that you previously cloned:

docker-compose up -d

This will launch the sofware as a daemon. To verify that everything is up and runnning, launch the script

bash services-checker.sh

Which will output the status of the containers

## Backup and restore

When launching the script, there's an option to restore the server files from a backup machine. Keep in mind that by doing so, everything will be overwrited
so you shouldn't launch the script with that option on a running server without the knowledge about what will happen.

Before proceeding with the restore operation setup the variables according to the README.md inside backup-restore directory, and follow the instructions
contained there.

## Repository update

### Update versions on repo

When a microservice is updated, we need to update both the repository and the machines using that version of the microservice.

To do that, we launch

- python3 .\release\compare_versions.py --gitlab-token {{token}}

Where token is a read-api token permission for group /boxio.

This script updates the config_file.json with the latest version of the software, taken from the last tagged commit on the branch

### Generate release for the component

We then launch the script to generate releases. This is

- python3 .\release\release_creation.py {{token}}

For all the services, it tries to create a release. If it already exists, then it does nothing.

### Generate release for the compose

When we plan to release a new version of the cloud BSP, we invoke

- python3 .\release\increase_version.py

This updates the BSP version. The next step is launching tagging and pushing the tag

Then we release

python3 .\release\build_release.py {{token}}

That generates a new release and notifies on slack the team about the new available version
