version: '3'
services:
    auth-database:
        image: registry.edalab.it/edalab/boxio/cloud/docker/postgres/safe-place:${AUTHDB_VERSION}
        container_name: auth-database-bk
        environment:
            - POSTGRES_USER=${AUTHDB_USER}
            - POSTGRES_PASSWORD=${AUTHDB_PASSWORD}
        logging:
            driver: 'json-file'
            options:
                max-size: '10M'
                max-file: '10'
        volumes:
            - ${AUTHDB_HOST_VOLUME}:${AUTHDB_CONTAINER_VOLUME}
            - ${AUTHDB_BK_HOST_VOLUME}:${AUTHDB_BK_CONTAINER_VOLUME}
        expose:
            - 5432
        restart: always
