def check_duplicates(file_path):
    values_set = set()
    duplicate_values = set()
    
    with open(file_path, 'r') as file:
        for line in file:
            if '=' in line:
                print(line.split('='))
                try:
                    key, value = map(str.strip, line.split('='))
                except:
                    continue
                if value in values_set:
                    duplicate_values.add(value)
                else:
                    values_set.add(value)
    
    return duplicate_values

file_path = '.env.bk'
duplicates = check_duplicates(file_path)

if duplicates:
    print(f'Duplicate values found: {", ".join(duplicates)}')
else:
    print('No duplicate values found.')
