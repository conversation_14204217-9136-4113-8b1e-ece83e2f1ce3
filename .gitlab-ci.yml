services:
  - alpine/git
stages:
  - deploy
variables:
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_DRIVER: overlay2

deploy-cloud-dev:
  stage: deploy
  image: paolopdp/ssh
  tags:
    - docker-compose-boxio
  script:
    - mkdir /root/.ssh
    - echo "$DEV_EDALAB_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - ssh-keyscan -H ************* >> ~/.ssh/known_hosts
    - ssh root@************* "cd /opt/boxio; git pull https://DOCKER_COMPOSE_TOKEN:$<EMAIL>/edalab/boxio/cloud/docker/docker-compose-boxio.git; docker-compose down; python3 release/finalize_release.py timescale-data-access; docker-compose up -d"
  environment:
    name: cloud-dev
    url: https://*************
  when: manual
  rules:
    - if: "$CI_COMMIT_TAG =~ /.*rc.*/"

deploy-etensil:
  stage: deploy
  image: paolopdp/ssh
  tags:
    - docker-compose-boxio
  script:
    - mkdir /root/.ssh
    - echo "$ETENSIL_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - ssh-keyscan -H etensil.edalab.it >> ~/.ssh/known_hosts
    - ssh <EMAIL> "cd /opt/boxio; git pull https://DOCKER_COMPOSE_TOKEN:$<EMAIL>/edalab/boxio/cloud/docker/docker-compose-boxio.git; docker-compose down; python3 release/finalize_release.py timescale-data-access; docker-compose up -d"
  environment:
    name: etensil
    url: https://etensil.edalab.it
  when: manual
  rules:
    - if: "$CI_COMMIT_TAG !~ /.*rc.*/"

deploy-100-27:
  stage: deploy
  image: paolopdp/ssh
  tags:
    - docker-compose-boxio
  script:
    - mkdir /root/.ssh
    - echo "$PRIVATE_TEST_MACHINE_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - ssh-keyscan -H ************** >> ~/.ssh/known_hosts
    - ssh root@************** "cd /opt/boxio; git pull https://DOCKER_COMPOSE_TOKEN:$<EMAIL>/edalab/boxio/cloud/docker/docker-compose-boxio.git; docker-compose down; python3 release/finalize_release.py timescale-data-access; docker-compose up -d"
  environment:
    name: **************
    url: https://**************
  when: manual
  rules:
    - if: "$CI_COMMIT_TAG =~ /.*rc.*/"
