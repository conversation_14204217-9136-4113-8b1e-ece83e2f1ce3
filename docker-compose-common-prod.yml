#
# Stefano <PERSON>: Hints for service comment to be used in services_checker.sh script
# service declaration must be prepended by a comment like
# # <service type>: <service name>
# <service type> can be oneshot (runs once and then stops) or persistent (typically a daemon)
# <sercice name> must match the container name as declared in the service
#
version: "3"

services:
  # persistent: mqtt
  mqtt:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/docker/emqttd/safe-place:${MQTT_VERSION}
    container_name: mqtt
    restart: always
    ports:
      - ${MQTT_SSL_PORT}:${MQTT_SSL_PORT}
    volumes:
      - ${MQTT_HOST_VOLUME}:${MQTT_CONTAINER_VOLUME}:ro
      - ${MQTT_HOST_PLUGINS_VOLUME}:${MQTT_CONTAINER_PLUGINS_VOLUME}
      - ${SSL_HOST_VOLUME}:${SSL_CONTAINER_VOLUME}:ro
      - ${BM_CERT_HOST_VOLUME}:${BM_CERT_CONTAINER_VOLUME}:ro
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    expose:
      - ${MQTT_SSL_PORT}
    environment:
      EMQX_AUTH__USER__1__USERNAME: ${MQTT_USER}
      EMQX_AUTH__USER__1__PASSWORD: ${MQTT_PASSWORD}
      EMQX_AUTH__USER__PASSWORD_HASH: ${MQTT_USER_PASSWORD_HASH}
      EMQX_ALLOW_ANONYMOUS: ${MQTT_ALLOW_ANONYMOUS}
      EMQX_LOADED_PLUGINS: ${MQTT_PLUGINS}
      EMQX_LOG__LEVEL: ${MQTT_LOG_LEVEL}
      EMQX_LISTENER__SSL__EXTERNAL__HANDSHAKE_TIMEOUT: 15s
      EMQX_LISTENER__WSS__EXTERNAL: "${MQTT_WSS_PORT}"
      EMQX_LISTENER__SSL__EXTERNAL: ${MQTT_SSL_PORT}
      EMQX_LISTENER__SSL__EXTERNAL__KEYFILE: ${SSL_CONTAINER_KEY}
      EMQX_LISTENER__SSL__EXTERNAL__CERTFILE: ${SSL_CONTAINER_CERT}
      EMQX_LISTENER__SSL__EXTERNAL__CACERTFILE: ${SSL_CONTAINER_CHAIN}
      EMQX_LISTENER__WSS__EXTERNAL__KEYFILE: ${SSL_CONTAINER_KEY}
      EMQX_LISTENER__WSS__EXTERNAL__CERTFILE: ${SSL_CONTAINER_CERT}
      EMQX_LISTENER__WSS__EXTERNAL__CACERTFILE: ${SSL_CONTAINER_CHAIN}
    env_file:
      - .env
    networks:
      boxio:

  # persistent: rabbitmq
  rabbitmq:
    image: rabbitmq:${RABBITMQ_VERSION}
    container_name: rabbitmq
    restart: always
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    # volumes:
    # - ${SSL_HOST_VOLUME}:${SSL_CONTAINER_VOLUME}:ro
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    env_file:
      - .env
    networks:
      boxio:

  # persistent: auth-database
  auth-database:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/docker/postgres/safe-place:${AUTHDB_VERSION}
    container_name: auth-database
    environment:
      - POSTGRES_USER=${AUTHDB_USER}
      - POSTGRES_PASSWORD=${AUTHDB_PASSWORD}
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    volumes:
      - ${AUTHDB_HOST_VOLUME}:${AUTHDB_CONTAINER_VOLUME}
    env_file:
      - .env

    restart: always
    networks:
      boxio:

  # oneshot: auth-update
  auth-update:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/docker/postgres/update-safe-place:${AUTHDB_UPDATE_VERSION}
    container_name: auth-update
    environment:
      - AUTHDB_USER=${AUTHDB_USER}
      - AUTHDB_PASSWORD=${AUTHDB_PASSWORD}
      - AUTHDB_BOXIO_USER=${AUTHDB_BOXIO_USER}
      - AUTHDB_BOXIO_PASSWORD=${AUTHDB_BOXIO_PASSWORD}
    env_file:
      - .env

    depends_on:
      - auth-database
    links:
      - auth-database
    networks:
      boxio:

  # persistent: boxio-manager
  boxio-manager:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/auth-manager/safe-place:${BOXIO_MANAGER_VERSION}
    stop_grace_period: 1s
    container_name: boxio-manager
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    volumes:
      - ${WEBAPPS_HOST_LANGUAGE_FILE}:${WEBAPPS_CONTAINER_LANGUAGE_FILE}:ro
      - ${BM_DEVICE_HOST_VOLUME}:${BM_DEVICE_CONTAINER_VOLUME}
      - ${BM_CERT_HOST_VOLUME}:${BM_CERT_CONTAINER_VOLUME}:ro
      - ${LANGUAGE_HOST_VOLUME}:${LANGUAGE_CONTAINER_VOLUME}:ro
      - ${ENDPOINT_JSON_HOST_VOLUME}:${ENDPOINT_JSON_CONTAINER_VOLUME}
      - ${WEBAPPS_HOST_VOLUME}/boxio-software:${WEBAPPS_CONTAINER_VOLUME}/boxio-software
      - ${TDA_CSV_HOST_VOLUME}:${TDA_CSV_CONTAINER_VOLUME}
    env_file:
      - .env
    depends_on:
      - auth-database
      - rabbitmq
      - mqtt
    links:
      - auth-database
      - rabbitmq
      - mqtt
    restart: always
    networks:
      boxio:

  # persistent: couchdb
  couchdb:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/docker/couchdb/safe-place:${COUCHDB_VERSION}
    container_name: couchdb
    environment:
      - COUCHDB_USER=${COUCHDB_USER}
      - COUCHDB_PASSWORD=${COUCHDB_PASSWORD}
    profiles:
      - cm
    env_file:
      - .env
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    volumes:
      - ${COUCHDB_HOST_VOLUME}:${COUCHDB_CONTAINER_VOLUME}
      - ${COUCHDB_HOST_SETUP_VOLUME}:${COUCHDB_CONTAINER_SETUP_VOLUME}
    restart: always
    networks:
      boxio:

  # oneshot: couch-update
  couch-update:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/docker/couchdb/update-safe-place:${COUCHDB_UPDATE_VERSION}
    container_name: couch-update
    profiles:
      - cm
    environment:
      - COUCHDB_USER=${COUCHDB_USER}
      - COUCHDB_PASSWORD=${COUCHDB_PASSWORD}
    env_file:
      - .env
    depends_on:
      - couchdb
    links:
      - couchdb
    networks:
      boxio:

  # persistent: configuration-manager
  configuration-manager:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/configuration-manager/safe-place:${CONFIGURATION_MANAGER_VERSION}
    stop_grace_period: 1s
    container_name: configuration-manager
    profiles:
      - cm
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    env_file:
      - .env
    volumes:
      - ${WEBAPPS_HOST_VOLUME}/boxio-software:${WEBAPPS_CONTAINER_VOLUME}/boxio-software
      - ${CM_IMAGE_HOST_FOLDER}:${CM_IMAGE_CONTAINER_FOLDER}

    depends_on:
      - couchdb
      - rabbitmq
      - boxio-manager
    links:
      - couchdb
      - rabbitmq
      - boxio-manager
      - timescale-data-access
    restart: always
    networks:
      boxio:

  # persistent: nimbus
  nimbus:
    image: storm:2.4.0
    stop_grace_period: 1s
    container_name: nimbus
    command: >
      storm nimbus
      -c storm.zookeeper.servers="[\"zookeeper\"]"
      -c nimbus.seeds="[\"nimbus\"]"
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    depends_on:
      - zookeeper
    links:
      - zookeeper
    ports:
      - 6627:6627
    environment:
      - STORM_VERSION=${STORM_VERSION}
    restart: always
    networks:
      boxio:

  # persistent: supervisor
  supervisor:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/storm/safe-place:${STORM_VERSION}
    stop_grace_period: 1s
    container_name: supervisor
    volumes:
      - ${STORM_LOG4J2_HOST_VOLUME}:${STORM_LOG4J2_CONTAINER_VOLUME}
    env_file:
      - .env
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    depends_on:
      - nimbus
      - zookeeper
    links:
      - nimbus
      - zookeeper
    environment:
      - STORM_VERSION=${STORM_VERSION}
    restart: always
    networks:
      boxio:

  zookeeper:
    image: zookeeper:latest
    stop_grace_period: 1s
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    container_name: zookeeper
    environment:
      - STORM_VERSION=${STORM_VERSION}
    restart: always
    networks:
      boxio:

  # persistent: webeditor
  webeditor:
    restart: always
    image: ${REGISTRY_NAME}/edalab/boxio/gui/monorepo-gui/${WE_FRONTEND_VERSION}
    stop_grace_period: 1s
    container_name: webeditor
    profiles:
      - we
    ports:
      - 443:443
      - 9997:9997
    env_file:
      - .env
    volumes:
      - ${WD_NGINX_HOST_VOLUME}:${WD_NGINX_CONTAINER_VOLUME}
      - ${WD_NGINX_DEFAULT_HOST_VOLUME}:${WD_NGINX_DEFAULT_CONTAINER_VOLUME}
      - ${SSL_HOST_VOLUME}:${SSL_CONTAINER_VOLUME}
      - ${WD_IMAGES_HOST_VOLUME}:${WD_IMAGES_CONTAINER_VOLUME}
      - ./nginx/nginx-entrypoint.sh:/docker-entrypoint.d/60-nginx-config.sh
    depends_on:
      - auth-database
      - configuration-manager
    networks:
      boxio:

  timescale-data-access:
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/timescale-data-access/safe-place:${TIMESCALE_DATA_ACCESS_VERSION}
    stop_grace_period: 1s
    container_name: timescale-data-access
    profiles:
      - tda
    depends_on:
      - boxio-manager
    logging:
      driver: ${LOG_DRIVER}
      options:
        max-size: ${LOG_MAX_SIZE}
        max-file: ${LOG_MAX_FILE}
    env_file:
      - .env
    environment:
      - JAVA_OPTS=${TS_JAVA_OPTS}

    volumes:
      - ${WEBAPPS_HOST_VOLUME}:${WEBAPPS_CONTAINER_VOLUME}
      - ${TDA_CSV_HOST_VOLUME}:${TDA_CSV_CONTAINER_VOLUME}
    links:
      - mqtt
    restart: always
    networks:
      boxio:

    #persistent
  mongodb:
    container_name: mongodb
    image: mongo:${MONGO_VERSION}
    profiles:
      - es
    volumes:
      - ${MONGO_HOST_VOLUME}:${MONGO_CONTAINER_VOLUME}
    restart: always
    networks:
      boxio:

  # persistent: event-manager
  event-manager:
    restart: always
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/event-service/image:${EVENT_MANAGER_VERSION}
    stop_grace_period: 1s
    container_name: event-manager
    profiles:
      - es
    volumes:
      - ${ES_FIREBASE_KEY_PATH_HOST}:${ES_FIREBASE_KEY_PATH}:ro
      - ${ES_IMAGE_FOLDER_HOST}${ES_IMAGE_FILE}:${ES_IMAGE_FOLDER_CONTAINER}${ES_IMAGE_FILE}
    env_file:
      - .env
    depends_on:
      - mongodb
      - rabbitmq
    networks:
      boxio:

  health-service:
    container_name: health-service
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/health-service/main:${HEALTH_SERVICE_VERSION}
    stop_grace_period: 1s
    profiles:
      - hs
    env_file:
      - .env
    depends_on:
      - auth-database
      - rabbitmq
    restart: always
    networks:
      boxio:

  # persistent: disk-size
  disk-size:
    restart: always
    env_file:
      - .env
    profiles:
      - ds
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/disk-size/safe-place:${DISK_SIZE_VERSION}
    container_name: disk-size
    depends_on:
      - rabbitmq
    networks:
      boxio:

  assistant-service:
    container_name: assistant-service
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/assistant-service/image:${ASSISTANT_SERVICE_VERSION}
    env_file:
      - .env
    depends_on:
      - mqtt
      - configuration-manager
    restart: always
    networks:
      boxio:

  call-service:
    container_name: call-service
    image: ${REGISTRY_NAME}/edalab/boxio/cloud/call-service/image:${CALL_SERVICE_VERSION}
    stop_grace_period: 1s
    env_file:
      - .env
    depends_on:
      - auth-database
      - rabbitmq
    restart: always
    networks:
      boxio:

  redis:
    container_name: redis
    image: redis:7.4.2-alpine
    volumes:
      - ${REDIS_HOST_FOLDER}/${REDIS_HOST_DATA_VOLUME}:${REDIS_CONTAINER_DATA_VOLUME}}
    restart: always
    networks:
      boxio:
    # command: ["redis-server", "--appendonly", "yes"]
