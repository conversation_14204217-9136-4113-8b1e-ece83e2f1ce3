[{"subsection": "GENERAL", "properties": [{"name": "HOST", "example": "cloud-test.edalab.it", "userInput": true, "description": "Host of the server"}, {"name": "HOST_CERTS", "example": "cloud-test-certs.edalab.it", "userInput": true, "description": "Host of the server for certificates"}, {"name": "LOG_LEVEL", "default": "INFO", "description": "Log level of cloud applications"}, {"name": "WORKDIRPATH", "default": "/app", "description": "TODO remove"}, {"name": "APPLICATION_PROPERTIES_FILE", "default": "/root/config/application.properties", "description": "Location of application.properties files inside the containers"}, {"name": "LANGUAGE_HOST_VOLUME", "default": "./language/", "description": "Host volume with the language files"}, {"name": "LANGUAGE_CONTAINER_VOLUME", "default": "/language/", "description": "Container volume with the language files"}, {"name": "DNS_NAME", "default": "${HOST}", "description": "Hostname"}, {"name": "REGISTRY_NAME", "enum": ["registry.edalab.it", "registry-dev.edalab.it:5000"], "userInput": true, "description": "Registry location"}, {"name": "ENDPOINT_JSON_HOST_VOLUME", "default": "./shared/config/endpoint.json", "description": "Host location of the endpoint.json file"}, {"name": "ENDPOINT_JSON_CONTAINER_VOLUME", "default": "/config/endpoint.json", "description": "Container location of the endpoint.json file"}, {"name": "ENDPOINT_JSON_VERSION", "isVersion": true, "externalFileReference": "shared/config/endpoint.json", "description": "Version of the endpoint.json file"}, {"name": "LOG_DRIVER", "default": "json-file", "description": "Driver for logging"}, {"name": "LOG_MAX_SIZE", "default": "10M", "description": "Max size before rotation"}, {"name": "LOG_MAX_FILE", "default": "10", "description": "Max number of files to hold"}, {"name": "GENERAL_PRIMARY_COLOR", "example": "19 18 70", "userInput": true, "description": "Primary color"}]}, {"subsection": "MQTT", "properties": [{"name": "MQTT_USER", "default": "boxio", "description": "Username of the mqtt user"}, {"name": "MQTT_PASSWORD", "default": "PBOXIO22017", "description": "Password of the mqtt user"}, {"name": "MQTT_VERSION", "isVersion": true, "versionFileReference": "MQTT", "description": "Version of the mqtt client"}, {"name": "MQTT_HOST_VOLUME", "default": "./emqx/opt/emqx/etc/acl.conf", "description": "Path for the host volume for the mqtt container"}, {"name": "MQTT_CONTAINER_VOLUME", "default": "/opt/emqx/etc/acl.conf", "description": "Path for the container volume for the mqtt container"}, {"name": "MQTT_HOST_PLUGINS_VOLUME", "default": "/opt/boxio/emqx/opt/emqx/etc/plugins", "description": "Path for the host volume for the plugins of mqtt container"}, {"name": "MQTT_CONTAINER_PLUGINS_VOLUME", "default": "/opt/emqx/etc/plugins", "description": "Path for the container volume for plugins of mqtt container"}, {"name": "MQTT_DOCKER_URL", "default": "ssl://mqtt:8883", "description": "Connection url to the mqtt container"}, {"name": "MQTT_DOCKER_API_URL", "default": "http://mqtt:18083", "description": "Connection url to the mqtt api interface"}, {"name": "MQTT_API_PASSWORD", "default": "4f030cb0-cc7b-11ed-afa1-0242ac120002", "description": "Password of the mqtt api interface"}, {"name": "MQTT_SSL_PORT", "default": "8883", "description": "MQTT port for secure access"}, {"name": "MQTT_HTTP_PORT", "default": "1883", "description": "MQTT port for http access"}, {"name": "MQTT_WSS_PORT", "default": "8085", "description": "MQTT port for WSS access"}, {"name": "MQTT_WS_PORT", "default": "8080", "description": "MQTT port for WS access"}, {"name": "MQTT_INTERFACE_PORT", "default": "18083", "description": "MQTT port for web api access"}, {"name": "MQTT_USER_PASSWORD_HASH", "default": "plain", "description": "Hash method for user password"}, {"name": "MQTT_ALLOW_ANONYMOUS", "default": "false", "description": "If anonymous login is allowed"}, {"name": "MQTT_PLUGINS", "default": "emqx_auth_mnesia,emqx_management,emqx_retainer,emqx_dashboard,emqx_auth_pgsql,emqx_auth_http", "description": "Allowed plugins on boot"}, {"name": "MQTT_LOG_LEVEL", "default": "info", "description": "Log severity for mqtt"}]}, {"subsection": "RABBITMQ", "properties": [{"name": "RABBITMQ_HOST", "default": "rabbitmq", "description": "Host of the rabbitmq container"}, {"name": "RABBITMQ_USER", "default": "rabbitmq_user", "description": "Username of the rabbitmq user"}, {"name": "RABBITMQ_PASSWORD", "autogenerated": true, "description": "Password of the rabbitmq user"}, {"name": "RABBITMQ_VERSION", "isVersion": true, "userInput": true, "default": "3.13.7-management", "description": "Version of the rabbitmq image"}]}, {"subsection": "AUTHDB", "properties": [{"name": "AUTHDB_TYPE", "default": "postgresql", "description": "Database type"}, {"name": "AUTHDB_HOST", "default": "auth-database", "description": "Host of the authdb container"}, {"name": "AUTHDB_USER", "default": "postgres_user", "description": "Username of the authdb user"}, {"name": "AUTHDB_PASSWORD", "autogenerated": true, "description": "Password of the authdb user"}, {"name": "AUTHDB_VERSION", "isVersion": true, "versionFileReference": "AUTHDB", "description": "Version of the authdb image"}, {"name": "AUTHDB_UPDATE_VERSION", "isVersion": true, "versionFileReference": "AUTHDB", "description": "Version of the authdb-update image"}, {"name": "AUTHDB_HOST_VOLUME", "default": "./auth-database/home/<USER>/pgdata/data", "description": "Path for the host volume for authdb "}, {"name": "AUTHDB_CONTAINER_VOLUME", "default": "/home/<USER>/pgdata/data", "description": "Path for the container volume for authdb "}, {"name": "AUTHDB_BK_HOST_VOLUME", "default": "./auth-database/remote_server.sql.gz", "description": "Path for the host volume for authdb backup"}, {"name": "AUTHDB_BK_CONTAINER_VOLUME", "default": "/remote_server.sql.gz", "description": "Path for the container volume for authdb backup"}, {"name": "AUTHDB_NAME", "default": "remote_server", "description": "Name of the database"}, {"name": "AUTHDB_HOST_PORT", "default": "5432", "description": "Connection port of the database"}, {"name": "AUTHDB_BOXIO_USER", "default": "<EMAIL>", "description": "Username of the global admin"}, {"name": "AUTHDB_BOXIO_PASSWORD", "autogenerated": true, "description": "Password of the global admin"}]}, {"subsection": "COUCHDB", "properties": [{"name": "COUCHDB_VERSION", "isVersion": true, "versionFileReference": "COUCHDB", "description": "Version of the couchdb image"}, {"name": "COUCHDB_HOST", "default": "couchdb", "description": "Host of the couchdb container"}, {"name": "COUCHDB_USER", "default": "couchdb_user", "description": "Username of the couchdb user"}, {"name": "COUCHDB_PORT", "default": "5984", "description": "Connection port of the couchdb database"}, {"name": "COUCHDB_SECURE", "default": "false", "description": "If couchdb connection requires TLS"}, {"name": "COUCHDB_PASSWORD", "autogenerated": true, "description": "Password of the couchdb user"}, {"name": "COUCHDB_UPDATE_VERSION", "isVersion": true, "versionFileReference": "COUCHDB", "description": "Version of the couchdb-update image"}, {"name": "COUCHDB_HOST_VOLUME", "default": "./couchdb/opt/couchdb/data", "description": "Path for the host volume for couchdb data"}, {"name": "COUCHDB_CONTAINER_VOLUME", "default": "/opt/couchdb/data", "description": "Path for the container volume for couchdb data"}, {"name": "COUCHDB_HOST_SETUP_VOLUME", "default": "./couchdb/etc/local.d", "description": "Path for the host volume for couchdb settings"}, {"name": "COUCHDB_CONTAINER_SETUP_VOLUME", "default": "/opt/couchdb/etc/local.d", "description": "Path for the container volume for couchdb settings"}, {"name": "COUCHDB_SSL_HOST_VOLUME", "default": "./certs/etc/ssl", "description": "Path for the host volume for couchdb ssl settings"}, {"name": "COUCHDB_SSL_CONTAINER_VOLUME", "default": "/certs/etc/ssl", "description": "Path for the container volume for couchdb ssl settings"}]}, {"subsection": "CONFIGURATION-MANAGER", "properties": [{"name": "CONFIGURATION_MANAGER_VERSION", "isVersion": true, "versionFileReference": "CONFIGURATION_MANAGER", "description": "Version of the configuration manager image"}, {"name": "CM_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If configuration manager module is enabled"}, {"name": "CM_CONFIG_HOST_VOLUME", "default": "./configuration-manager${APPLICATION_PROPERTIES_FILE}", "description": "Path for the host volume for configuration manager settings"}, {"name": "CM_CONFIG_CONTAINER_VOLUME", "default": "${APPLICATION_PROPERTIES_FILE}", "description": "Path for the container volume for configuration manager settings"}, {"name": "CM_HOST", "default": "configuration-manager", "description": "Host of the configuration-manager container"}, {"name": "CM_PREFIX", "default": "/cm", "description": "Prefix for configuration-manager apis"}, {"name": "CM_HTTP_PORT", "default": "8089", "description": "Port for http communication with configuration-manager"}, {"name": "CM_HTTPS_PORT", "default": "9999", "description": "Port for https communication with configuration-manager"}, {"name": "CM_DOCKER_URL", "default": "http://configuration-manager:${CM_HTTP_PORT}${CM_PREFIX}", "description": "Url for internal communication with configuration manager"}, {"name": "CM_FIRMWARE_FOLDER", "default": "/config/boxio-software", "description": "Path in the container where to save BSP"}, {"name": "CM_TOMCAT_FOLDER", "default": "/boxio-software", "description": "Path in the container where to send BSP"}, {"name": "CM_INSTALLATIONS_NAME", "default": "installations", "description": "Installations database name in couchdb"}, {"name": "CM_BOXIOS_NAME", "default": "boxios", "description": "Boxios database name in couchdb"}, {"name": "CM_JSONSCHEMA_FOLDER", "default": "json-schemas/", "description": "Json-schemas location in configuration-manager folder"}, {"name": "CM_JSONSCHEMA_VERSION", "default": "3_0", "description": "Version of the json-schema which should be used"}, {"name": "CM_JSONSCHEMA_CONFIGURATION_FOLDER", "default": "/configuration/v3/", "description": "Json-schema configuration location"}, {"name": "CM_JSONSCHEMA_PERMISSION_FOLDER", "default": "/permission/", "description": "Json-schema permission location"}, {"name": "CM_JSONSCHEMA_AUTOMATISM_STORE_FILE", "default": "/configuration/v3/boxio_automatism.schema.json", "description": "Json-schema automatism store file location"}, {"name": "CM_JSONSCHEMA_STRUCTURE_STORE_FILE", "default": "/configuration/v3/boxio_structure.schema.json", "description": "Json-schema structure store file location"}, {"name": "CM_JSONSCHEMA_AUTOMATISM_CONF_FILE", "default": "/configuration/v3/store_automatism.schema.json", "description": "Json-schema automatism conf store file location"}, {"name": "CM_JSONSCHEMA_STRUCTURE_CONF_FILE", "default": "/configuration/v3/store_configuration.schema.json", "description": "Json-schema structure conf store file location"}, {"name": "CM_JSONSCHEMA_SET_PERMISSION_FILE", "default": "/permission/set_permission.schema.json", "description": "Json-schema permission  file location"}, {"name": "CM_JSONSCHEMA_CLOUD_AUTOMATISM_CONF_FILE", "default": "cloud-engine-automatism-schema/schema.json", "description": "Json-schema cloud automatism file location"}, {"name": "CM_RETENTION_SIZE", "default": "3GB", "description": "Default retention size for new installations"}, {"name": "CM_RETENTION_DISK_SIZE_API", "default": "http://disk-size:9056/disk", "description": "Api to call to retrieve occupancy of the disk"}, {"name": "CM_TDA_ENABLED", "default": "false", "description": "If configuration-manager needs to communicate with timescale-data-access"}, {"name": "CM_IMAGE_HOST_FOLDER", "default": "./configuration-manager/images", "description": "Location of the maps inside host machine"}, {"name": "CM_IMAGE_CONTAINER_FOLDER", "default": "/images", "description": "Location of the maps inside container machine"}, {"name": "CM_STANDALONE_MODE", "default": "false", "description": "If standalone mode is active (server acts as restapi)"}, {"name": "CM_STANDALONE_HOST_FOLDER", "default": "/opt/else", "description": "Else folder"}, {"name": "CM_STANDALONE_CONTAINER_FOLDER", "default": "/else", "description": "Else folder"}, {"name": "CM_STANDALONE_STRUCTURE_CONTAINER", "default": "/else/structure.json", "description": "Location of the structure in standalone mode"}, {"name": "CM_STANDALONE_STRUCTURE_HOST", "default": "/opt/else/structure.json", "description": "Location of the structure in standalone mode"}, {"name": "CM_STANDALONE_AUTOMATISM_CONTAINER", "default": "/else/automatism.json", "description": "Location of the automatism in standalone mode"}, {"name": "CM_STANDALONE_AUTOMATISM_HOST", "default": "/opt/else/automatism.json", "description": "Location of the automatism in standalone mode"}, {"name": "CM_RABBITMQ_EXCHANGE_NAME", "default": "configuration-service-exchange", "description": "Rabbitmq exchange name for configuration service"}, {"name": "CM_RABBITMQ_QUEUE_NAME", "default": "configuration-service", "description": "Rabbitmq queue name for configuration service"}]}, {"subsection": "BOXIO-MANAGER", "properties": [{"name": "BOXIO_MANAGER_VERSION", "isVersion": true, "versionFileReference": "BOXIO_MANAGER", "description": "Version of the boxio manager image"}, {"name": "BM_GHOME_URL", "default": "https://oauth-redirect.googleusercontent.com/r/newagent-arqqiy", "description": "Url used to connect to google home"}, {"name": "BM_CONFIG_HOST_VOLUME", "default": "./boxio-manager${APPLICATION_PROPERTIES_FILE}", "description": "Path for the host volume for boxio manager settings"}, {"name": "BM_CONFIG_CONTAINER_VOLUME", "default": "${APPLICATION_PROPERTIES_FILE}", "description": "Path for the container volume for boxio manager settings"}, {"name": "BM_CERT_HOST_VOLUME", "default": "./boxio-manager/certs", "description": "Path for the host volume for boxio manager jwt settings"}, {"name": "BM_CERT_CONTAINER_VOLUME", "default": "/certs", "description": "Path for the container volume for boxio manager jwt settings"}, {"name": "BM_DEVICE_HOST_VOLUME", "default": "./boxio-manager/device", "description": "Path for the host volume for boxio manager to store device information"}, {"name": "BM_DEVICE_CONTAINER_VOLUME", "default": "/device", "description": "Path for the container volume for boxio manager to store device information"}, {"name": "BM_ENDPOINT_CONFIGURATION", "default": "/config/endpoint.json", "description": "Path for the endpoint.json file"}, {"name": "BM_JWT_KEYSTORE_PASSWORD", "autogenerated": true, "description": "Password for the JWT keystore"}, {"name": "BM_JWT_KEYSTORE", "default": "/certs/keystore.p12", "description": "Location of the keystore file"}, {"name": "BM_JWT_KEYSTORE_ALIAS", "default": "server", "description": "<PERSON><PERSON> for jwt key"}, {"name": "BM_TRUSTSTORE_PASSWORD", "default": "37@l@b75HOcu", "description": "Truststore password"}, {"name": "BM_TRUSTSTORE", "default": "/certs/truststore.jks", "description": "Truststore location"}, {"name": "BM_TRUSTSTORE_AUTH", "default": "want", "description": "Truststore directive"}, {"name": "BM_HTTP_PORT", "default": "8081", "description": "Port for http communication with boxio-manager"}, {"name": "BM_HTTPS_PORT", "default": "9997", "description": "Port for https communication with boxio-manager"}, {"name": "BM_PREFIX", "default": "/am", "description": "Prefix for boxio-manager apis"}, {"name": "BM_AUTH_URL", "default": "https://${HOST}:${BM_HTTPS_PORT}${BM_PREFIX}", "description": "Url for  communication with boxio manager"}, {"name": "BM_DOCKER_URL", "default": "http://boxio-manager:${BM_HTTP_PORT}${BM_PREFIX}", "description": "Url for internal  communication with boxio manager"}, {"name": "BM_APP_NAME", "default": "BOX-IO", "description": "Application name"}, {"name": "BM_APP_ACCOUNT", "default": "BOX-IO", "description": "Application account"}, {"name": "BM_APP_FRONTEND_ADDRESS", "default": "https://${HOST}", "description": "Address of the frontend"}, {"name": "BM_APP_FRONTEND_URL", "default": "${BM_APP_FRONTEND_ADDRESS}", "description": "Address of the frontend"}, {"name": "BM_APP_REGISTRATION_URL", "default": "${BM_APP_FRONTEND_URL}/am/user/confirmRegistration/", "description": "Confirm registration page url"}, {"name": "BM_APP_FORGOT_URL", "default": "${BM_APP_FRONTEND_URL}/user/forgotPassword", "description": "Forgot password page url"}, {"name": "BM_APP_SUCCESS_URL", "default": "${BM_APP_FRONTEND_URL}/success", "description": "Success landing page url"}, {"name": "BM_APP_DEFAULT_PORTAL", "default": "false", "description": "Use default portal inside the application"}, {"name": "BM_REQUIRE_SSL", "default": "true", "description": "If the backend requires SSL"}, {"name": "BM_IS_INDUSTRIAL", "default": "true", "description": "If the application is industrial or consumer"}, {"name": "BM_DEVICE_PROVISIONING_PATH", "default": "${BM_DEVICE_CONTAINER_VOLUME}", "description": "Path for device provisioning"}, {"name": "BM_DEVICE_PROVISIONING_MD5", "default": "9d965ffe4ecda4043bd10731cee3672a", "description": "MD5 of the provisioning script"}, {"name": "BM_CLIENT_ID", "default": "ILabnJ7cexPrEEaeqXJJCH", "description": "Client identifier for boxio-manager"}, {"name": "BM_CLIENT_SECRET", "default": "3a22eef7-2de1-4228-a173-4661593f1903", "description": "Client secret for boxio manager"}, {"name": "BM_TRANSLATION_PARAMETER", "default": "/config/translations_parameters.json", "description": "File containing the translations for boxio parameters"}, {"name": "BM_TRANSLATION_FRONTEND", "default": "/language/", "description": "Path to the folder containing translations"}, {"name": "BM_DB_SCHEMA", "default": "amg", "description": "Schema of the boxio manager database"}, {"name": "BM_DEVICE_PATH", "default": "/device", "description": "Path to boxio manager device folder"}, {"name": "BM_JWT_PUBKEY", "default": "/certs/pubkey.pem", "description": "Public key file for certificates"}, {"name": "BM_JWT_KID", "default": "1c8bda7a-ca89", "description": "JWT kid used for jitsi"}, {"name": "BM_JITSI_ISSUER", "default": "a62d1194-17da-11e8-b642", "description": "JITSI issuer"}, {"name": "BM_JWT_GOOGLE_KEYS", "default": "https://www.googleapis.com/oauth2/v3/certs", "description": "Google keys for JWT"}, {"name": "BM_JWT_GATEWAY_ISSUER", "default": "https://${HOST}/", "description": "JWT issuer for boxio manager"}, {"name": "BM_JWK_KEY_SET_URI", "default": "https://${HOST}/am/.well-known/jwks.json", "description": "JWK keyset"}, {"name": "BM_JWT_JWK_SET_URI", "default": "https://${HOST}/am/.well-known/jwks.json", "description": "JWK keyset"}, {"name": "BM_GOOGLE_CLIENT_ID", "default": "************-hh8734momj08l2qah4s7bsovk92hio8q.apps.googleusercontent.com", "description": "Client identifier for Google"}, {"name": "BM_GOOGLE_CLIENT_SECRET", "default": "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", "description": "Client secret for google"}, {"name": "BM_GOOGLE_CHECK_TOKEN_URL", "default": "https://www.googleapis.com/oauth2/v3/tokeninfo?access_token", "description": "Url to check google access token"}, {"name": "BM_GOOGLE_TOKEN_URL", "default": "https://accounts.google.com/o/oauth2/token", "description": "URL to retrieve google token"}, {"name": "BM_GOOGLE_REDIRECT_URL", "default": "https://${HOST}/callback", "description": "Callback for google connection"}, {"name": "BM_RABBITMQ_EXCHANGE_NAME", "default": "boxio-manager-exchange", "description": "Rabbitmq exchange name for boxio manager"}, {"name": "BM_RABBITMQ_QUEUE_NAME", "default": "boxio-manager-service", "description": "Rabbitmq queue name for boxio manager"}]}, {"subsection": "STORM", "properties": [{"name": "STORM_VERSION", "isVersion": true, "versionFileReference": "STORM", "description": "Version of the storm image"}, {"name": "STORM_CONFIG_HOST_VOLUME", "default": "./storm/root/config", "description": "Path for the host volume for storm settings"}, {"name": "STORM_CONFIG_CONTAINER_VOLUME", "default": "/root/config", "description": "Path for the container volume for storm settings"}, {"name": "STORM_LOG4J2_HOST_VOLUME", "default": "./storm/log4j2/worker.xml", "description": "Path for the host volume for storm logs"}, {"name": "STORM_LOG4J2_CONTAINER_VOLUME", "default": "/apache-storm-2.4.0/log4j2/worker.xml", "description": "Path for the container volume for storm logs"}, {"name": "STORM_CLIENT_ID", "default": "6ZvV6CxOJ0C_CdIkwsaZ0w", "description": "Storm client identifier"}, {"name": "STORM_CLIENT_SECRET", "default": "4f030cb0-cc7b-11ed-afa1-0242ac120002", "description": "Storm client secret"}, {"name": "STORM_MQTT_RETRY", "default": "10000", "description": "Ms to wait before retrying to connect to mqtt"}, {"name": "STORM_REDIS_HOST", "default": "redis", "description": "Redis database host"}, {"name": "STORM_REDIS_PORT", "default": "6379", "description": "Redis database port"}, {"name": "STORM_REDIS_PASSWORD", "default": "", "description": "Redis password "}, {"name": "STORM_RABBITMQ_QUEUE_NAME", "default": "cloudengine-service", "description": "Rabbitmq queue name for storm service"}, {"name": "STORM_RABBITMQ_EXCHANGE_NAME", "default": "cloudengine-service-exchange", "description": "Rabbitmq exchange name for storm service"}]}, {"subsection": "TIMESCALE_DATA_ACCESS", "properties": [{"name": "TIMESCALE_DATA_ACCESS_VERSION", "isVersion": true, "versionFileReference": "TIMESCALE_DATA_ACCESS", "description": "Version of the TDA image"}, {"name": "TDA_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If timescale-data-access module is enabled"}, {"name": "TDA_CONFIG_HOST_VOLUME", "default": "./timescale-data-access${APPLICATION_PROPERTIES_FILE}", "description": "Path for the host volume for timescale data access settings"}, {"name": "TDA_CONFIG_CONTAINER_VOLUME", "default": "${APPLICATION_PROPERTIES_FILE}", "description": "Path for the container volume for timescale data access settings"}, {"name": "TDA_CSV_HOST_VOLUME", "default": "./timescale-data-access/tmp/exportCsv", "description": "Path where csv export files are saved in the host machine"}, {"name": "TDA_CSV_CONTAINER_VOLUME", "default": "/tmp/exportCsv", "description": "Path where csv export files are saved inside the container"}, {"name": "TDA_HTTPS_PORT", "default": "9998", "description": "Starting https port for timescale-data-access"}, {"name": "TDA_HTTP_PORT", "default": "8083", "description": "Starting http port for timescale-data-access"}, {"name": "TDA_PREFIX", "default": "/tda", "description": "Prefix for timescale-data-access apis"}, {"name": "TDA_DOCKER_URL", "default": "http://timescale-data-access:${TDA_HTTP_PORT}${TDA_PREFIX}", "description": "General url for internal communication towards timescale-data-access"}, {"name": "TDA_CSV_LIMIT", "default": "500000", "description": "CSV rows limit"}, {"name": "TDA_MQTT_CLIENT_ID", "default": "timescale-data-access", "description": "Client identifier of timescale-data-access for mqtt access"}, {"name": "TDA_CLIENT_ID", "default": "49924bd8-3150-4964-a70c", "description": "Client identifier of timescale-data-access for jwt token"}, {"name": "TDA_CLIENT_SECRET", "default": "523c3def-2b4b-4122-911d", "description": "Client credentials of timescale-data-access for jwt token"}, {"name": "TDA_ACTIVATE_SCHEDULE", "default": "true", "description": "If timescale-data-access should clean the disk"}, {"name": "TDA_UPLOAD_MAX_SIZE", "default": "10000000", "description": "Max uploadable size via API"}, {"name": "TDA_SCHEDULE_PERCENTAGE", "default": "0.95", "description": "An installation that reaches this percentage of the max size should be cleaned"}, {"name": "TDA_CLEAN_TIMEOUT", "default": "10800000", "description": "MS after which the clean algorithm should be called"}, {"name": "TDA_STATS_TIMEOUT", "default": "600000", "description": "MS after which the stats algorithm should be called"}, {"name": "TDA_STATS_ACTIVE", "default": "true", "description": "If stats algorithm is active"}, {"name": "TDA_POWER_CONSUMPTION_DELAY", "default": "300000", "description": "MS after which the power consumption algorithm should be called"}, {"name": "TS_JAVA_OPTS", "default": "-Xmx4096m", "description": "Java option to be used when launching the timescale-data-access container"}, {"name": "TDA_RABBITMQ_EXCHANGE_NAME", "default": "timescale-data-access-exchange", "description": "Rabbitmq exchange name for tda service"}, {"name": "TDA_RABBITMQ_QUEUE_NAME", "default": "timescale-data-access-service", "description": "Rabbitmq queue name for tda service"}]}, {"subsection": "SSL", "properties": [{"name": "SSL_CONTAINER_VOLUME", "default": "/etc/ssl/private/boxio", "description": "Path for the container volume for ssl certs"}, {"name": "SSL_HOST_VOLUME", "default": "./certs/etc/ssl/private/boxio", "description": "Path for the host volume for ssl certs"}, {"name": "SSL_HOST_VOLUME_KEY", "default": "./certs/etc/ssl/private/boxio/server/privkey.pem", "description": "Path for the file  with the key for ssl volume"}, {"name": "SSL_HOST_VOLUME_CERT", "default": "./certs/etc/ssl/private/boxio/server/cert.pem", "description": "Path for the file  with the certificate for ssl volume"}, {"name": "SSL_HOST_VOLUME_CA", "default": "./certs/etc/ssl/private/ca.pem", "description": "Path for the file  with the CA certificate for ssl volume"}, {"name": "SSL_CONTAINER_KEY", "default": "/etc/ssl/private/boxio/server/privkey.pem", "description": "Path for the file  with the key for ssl volume inside the container"}, {"name": "SSL_CONTAINER_CERT", "default": "/etc/ssl/private/boxio/server/cert.pem", "description": "Path for the file  with the certificate for ssl volume inside the container"}, {"name": "SSL_CONTAINER_CHAIN", "default": "/etc/ssl/private/boxio/server/chain.pem", "description": "Path for the file  with the certificate chain for ssl volume inside the container"}, {"name": "KEYSTORE_NAME", "default": "keystore.p12", "description": "Name of the keystore with the ssl certs inside it"}, {"name": "JWT_PUBLIC_KEY", "autogenerated": true, "description": "Public key of the keystore generated by script"}, {"name": "KEYSTORE_PASSWORD", "autogenerated": true, "description": "Password of the keystore"}, {"name": "KEYSTORE_TYPE", "default": "PKCS12", "description": "Keystore type"}, {"name": "KEYSTORE_ALIAS", "default": "boxio", "description": "<PERSON><PERSON> alias"}]}, {"subsection": "WEB_DASHBOARD", "properties": [{"name": "WEBAPPS_HOST_VOLUME", "default": "./shared/config/", "description": "Host location of resources used by multiple services (translations, endpoint.json)"}, {"name": "WEBAPPS_CONTAINER_VOLUME", "default": "/config/", "description": "Container location of resources used by multiple services (translations, endpoint.json)"}, {"name": "WEBAPPS_CONTAINER_LANGUAGE_FILE", "default": "/config/translations_parameters.json", "description": "Container location of the file used for the translation of the parameter names"}, {"name": "WEBAPPS_HOST_LANGUAGE_FILE", "default": "./shared/config/translations_parameters.json", "description": "Host location of the file used for the translation of the parameter names"}, {"name": "WD_NGINX_HOST_VOLUME", "default": "./nginx/conf.d/base.conf.template", "description": "Nginx config location in the host"}, {"name": "WD_NGINX_CONTAINER_VOLUME", "default": "/etc/nginx/conf.d/base.conf.template", "description": "Nginx config location in the container"}, {"name": "WD_NGINX_JITSI_HOST_VOLUME", "default": "./nginx/conf.d/jitsi.conf.template", "description": "Nginx jitsi config location in the host"}, {"name": "WD_NGINX_JITSI_CONTAINER_VOLUME", "default": "/etc/nginx/conf.d/jitsi.conf.template", "description": "Nginx config location in the container"}, {"name": "WD_NGINX_DEFAULT_HOST_VOLUME", "default": "./nginx/nginx.conf", "description": "Nginx general config location in the host"}, {"name": "WD_NGINX_DEFAULT_CONTAINER_VOLUME", "default": "/etc/nginx/nginx.conf", "description": "Nginx general config location in the container"}, {"name": "WD_IMAGES_HOST_VOLUME", "default": "./webeditor/images", "description": "Path of the host folder containing images for web-editor"}, {"name": "WD_IMAGES_CONTAINER_VOLUME", "default": "/media", "description": "Path of the container folder containing images for web-editor"}, {"name": "REFRESH_INTERVAL", "default": "36000", "description": "TBD"}, {"name": "SERVER_URL", "default": "${HOST}", "description": "TBD"}, {"name": "APP_ADDRESS", "default": "${HOST}", "description": "TBD"}]}, {"subsection": "MONGO", "properties": [{"name": "MONGO_HOST", "default": "mongodb", "description": "Mongo container host url"}, {"name": "MONGO_HOST_VOLUME", "default": "/mongodb/data/db", "description": "Mongo host volume containing the db data"}, {"name": "MONGO_CONTAINER_VOLUME", "default": "/data/db", "description": "Mongo container volume containing the db data"}, {"name": "MONGO_PORT", "default": "27017", "description": "Port to connect to mongo db"}, {"name": "MONGO_VERSION", "isVersion": true, "userInput": true, "description": "Version of the mongodb service image"}]}, {"subsection": "EVENT_SERVICE", "properties": [{"name": "EVENT_MANAGER_VERSION", "isVersion": true, "versionFileReference": "EVENT_MANAGER", "description": "Version of the event service image"}, {"name": "ES_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If event-service module is enabled"}, {"name": "ES_EMAIL_HOST", "default": "smtp-relay.sendinblue.com", "description": "Event service email host"}, {"name": "ES_EMAIL_PORT", "default": "587", "description": "Event service email port"}, {"name": "ES_EMAIL_USER", "default": "<EMAIL>", "description": "Event service email username"}, {"name": "ES_EMAIL_FROM", "default": "<EMAIL>", "description": "Event service email sender"}, {"name": "ES_EMAIL_PASSWORD", "default": "P4x1htRSvF8L3gn0", "description": "Event service email password"}, {"name": "ES_USERS_MANAGEMENT_PAGE_URL", "default": "https://${HOST}/userManagement", "description": "Event service location for the user management page"}, {"name": "ES_USER_EMAIL_UNREGISTER_URL", "default": "https://${HOST}/unregister", "description": "Event service location for the unregister page"}, {"name": "ES_MONGO_HOST", "default": "${MONGO_HOST}", "description": "Mongo container host url"}, {"name": "ES_MONGO_PORT", "default": "${MONGO_PORT}", "description": "Port to connect to mongo db"}, {"name": "ES_MONGO_DATABASE", "default": "event-service", "description": "Event service mongo database"}, {"name": "ES_RABBIT_HOST", "default": "${RABBITMQ_HOST}", "description": "Rabbitmq hostname used by event service to connect"}, {"name": "ES_RABBIT_PORT", "default": "5672", "description": "Rabbitmq port used by event service to connect"}, {"name": "ES_RABBIT_USERNAME", "default": "${RABBITMQ_USER}", "description": "Rabbitmq username used by event service to connect"}, {"name": "ES_RABBIT_PASSWORD", "default": "${RABBITMQ_PASSWORD}", "description": "Rabbitmq port used by event service to connect"}, {"name": "ES_RABBIT_QUEUE", "default": "event-service", "description": "Rabbitmq queue used by event service to communicate"}, {"name": "ES_REFRESH_TOKEN_VALIDITY", "default": "2629746000", "description": "JWT refresh token duration"}, {"name": "ES_FIREBASE_KEY_PATH_HOST", "default": "./push-service/firebase/firebase.json", "description": "Location of the firebase key in the host"}, {"name": "ES_FIREBASE_KEY_PATH", "default": "/firebase/firebase.json", "description": "Location of the firebase key in the container"}, {"name": "ES_DOCKER_URL", "default": "http://event-manager:3000", "description": "Connection url to the event service container"}, {"name": "ES_IMAGE_FOLDER_HOST", "default": "./event-service/images/", "description": "Logo to be displayed in the email"}, {"name": "ES_IMAGE_FOLDER_CONTAINER", "default": "/images/", "description": "Logo to be displayed in the email"}, {"name": "ES_IMAGE_FILE", "default": "logo.png", "description": "Logo to be displayed in the email"}, {"name": "ES_LOGO_PATH", "default": "${ES_IMAGE_FOLDER_CONTAINER}${ES_IMAGE_FILE}", "description": "Logo to be displayed in the email"}, {"name": "ES_PRIMARY_COLOR", "default": "${GENERAL_PRIMARY_COLOR}", "description": "Color for button in email"}, {"name": "ES_RABBITMQ_QUEUE_NAME", "default": "event-service", "description": "Rabbitmq queue name for event service"}, {"name": "ES_POSTGRES_URL", "default": "postgresql://${AUTHDB_USER}:${AUTHDB_PASSWORD}@${AUTHDB_HOST}:${AUTHDB_HOST_PORT}/${AUTHDB_NAME}?schema=event", "description": "Postgres connection string"}, {"name": "ES_SERVER_URL", "default": "${HOST}", "description": "Event service server url"}, {"name": "ES_SERVER_NAME", "example": "Cloud Dev <PERSON>", "userInput": true, "description": "Event service server name for email"}]}, {"subsection": "HEALTH_SERVICE", "properties": [{"name": "HEALTH_SERVICE_VERSION", "isVersion": true, "versionFileReference": "HEALTH_SERVICE", "description": "Version of the health service image"}, {"name": "HS_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If health service module is enabled"}, {"name": "HS_CONFIG_HOST_VOLUME", "default": "./health-service${APPLICATION_PROPERTIES_FILE}", "description": "Path for the host volume for health service settings"}, {"name": "HS_CONFIG_CONTAINER_VOLUME", "default": "${APPLICATION_PROPERTIES_FILE}", "description": "Path for the container volume for health service settings"}, {"name": "HS_HTTPS_PORT", "default": "9996", "description": "Starting https port for health service"}, {"name": "HS_REQUIRE_SSL", "default": "true", "description": "If health service requires SSL"}, {"name": "HS_DB_SCHEMA", "default": "health", "description": "Database schema used by health service"}, {"name": "HS_RABBITMQ_EXCHANGE_NAME", "default": "health-service-exchange", "description": "Rabbitmq exchange name for health service"}, {"name": "HS_RABBITMQ_QUEUE_NAME", "default": "health-service", "description": "Rabbitmq queue name for health service"}, {"name": "HS_MONGODB_URI", "default": "mongodb://${MONGO_HOST}:${MONGO_PORT}/measurement", "description": "Rabbitmq queue name for health service"}]}, {"subsection": "DISK_SIZE", "properties": [{"name": "DISK_SIZE_VERSION", "isVersion": true, "versionFileReference": "DISK_SIZE", "description": "Version of the disk size image"}, {"name": "DS_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If disk service module is enabled"}, {"name": "DS_RABBITMQ_QUEUE_NAME", "default": "disk-service", "description": "Rabbitmq queue name for disk service"}]}, {"subsection": "JITSI", "properties": [{"name": "JITSI_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If jitsi module is enabled"}, {"name": "JITSI_VERSION", "default": "9220", "description": "Current jitsi version"}]}, {"subsection": "ASSISTANT_SERVICE", "properties": [{"name": "ASSISTANT_SERVICE_VERSION", "isVersion": true, "versionFileReference": "ASSISTANT_SERVICE", "description": "Version of the assistant service image"}, {"name": "AS_MQTT_PROTOCOL", "default": "wss", "description": "Protocol"}, {"name": "AS_MQTT_HOST", "default": "${HOST}", "description": "Protocol"}, {"name": "AS_MQTT_PORT", "default:": 443, "description": "Mqtt port"}, {"name": "AS_MQTT_CLIENT_ID", "default": "assistant-service", "description": "Version of the assistant service image"}, {"name": "AS_MQTT_USERNAME", "default": "6ZvV6CxOJ0C_CdIkwsaZ0w", "description": "Version of the assistant service image"}, {"name": "AS_MQTT_PASSWORD", "default": "4f030cb0-cc7b-11ed-afa1-0242ac120002", "description": "Version of the assistant service image"}, {"name": "AS_RABBIT_HOST", "default": "${RABBITMQ_HOST}", "description": "Rabbitmq host"}, {"name": "AS_RABBIT_PORT", "default": "5672", "description": "Rabbitmq port"}, {"name": "AS_RABBIT_USERNAME", "default": "${RABBITMQ_USER}", "description": "Rabbitmq username"}, {"name": "AS_RABBIT_PASSWORD", "default": "${RABBITMQ_PASSWORD}", "description": "Rabbitmq password"}]}, {"subsection": "CALL_SERVICE", "properties": [{"name": "CALL_SERVICE_VERSION", "example": "0.0.0", "description": "Current call service version", "versionFileReference": "CALL_SERVICE"}, {"name": "CS_RABBIT_HOST", "default": "${RABBITMQ_HOST}", "description": "Rabbitmq hostname used by event service to connect"}, {"name": "CS_RABBIT_PORT", "default": "5672", "description": "Rabbitmq port used by event service to connect"}, {"name": "CS_RABBIT_USERNAME", "default": "${RABBITMQ_USER}", "description": "Rabbitmq username used by event service to connect"}, {"name": "CS_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If call-service module is enabled"}, {"name": "CS_RABBIT_PASSWORD", "default": "${RABBITMQ_PASSWORD}", "description": "Rabbitmq port used by event service to connect"}, {"name": "CS_RABBIT_QUEUE", "default": "call-service", "description": "Rabbitmq queue used by event service to communicate"}, {"name": "CS_JWT_SECRET", "example": "jwtsecret", "userInput": true, "description": "JWT secret used to sign tokens provided by getstream.io plaform, the video call provider"}, {"name": "CS_POSTGRES_URL", "default": "postgresql://${AUTHDB_USER}:${AUTHDB_PASSWORD}@${AUTHDB_HOST}:${AUTHDB_HOST_PORT}/call_service", "description": "Postgres connection string"}, {"name": "CS_RABBITMQ_QUEUE_NAME", "default": "call-service", "description": "Rabbitmq queue name for call service"}]}, {"subsection": "REDIS", "properties": [{"name": "REDIS_VERSION", "default": "7.4.2", "description": "Redis version"}, {"name": "REDIS_HOST_FOLDER", "default": "./redis", "description": "Redis host folder"}, {"name": "REDIS_HOST_DATA_VOLUME", "default": "/data", "description": "Redis host data volume"}, {"name": "REDIS_CONTAINER_DATA_VOLUME", "default": "/data", "description": "Path for the host volume for configuration manager settings"}]}, {"subsection": "DISK_SIZE", "properties": [{"name": "WE_TITLE", "example": "Webeditor", "userInput": true, "description": "Webeditor page title"}, {"name": "WE_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If timescale-data-access module is enabled"}, {"name": "WE_FAVICON", "example": "/media/favicon.ico", "userInput": true, "description": "Webeditor favicon container location"}, {"name": "WE_LOGO", "example": "/media/logo.png", "userInput": true, "description": "Webeditor logo container location"}, {"name": "WE_FRONTEND_VERSION", "isVersion": true, "versionFileReference": "WE_FRONTEND", "description": "Version of the event service image"}, {"name": "WE_APP_URL", "default": "${HOST}", "description": "Hostname"}, {"name": "WE_LAST_UPDATE", "default": "1683811963000", "description": "Webeditor last version update"}, {"name": "WE_BOXIO_MANAGER_PROTOCOL", "default": "https", "description": "Communication protocol used by boxio-manager"}, {"name": "WE_BOXIO_MANAGER_PORT", "default": "443", "description": "Port used by boxio-manager"}, {"name": "WE_BOXIO_MANAGER_URL", "default": "${HOST}", "description": "URL for boxio-manager"}, {"name": "WE_MQTT_PROTOCOL", "default": "wss", "description": "Communication protocol used by mqtt"}, {"name": "WE_MQTT_PORT", "default": "443", "description": "Port used by mqtt"}, {"name": "WE_MQTT_URL", "default": "${HOST}", "description": "URL for mqtt"}, {"name": "WE_FONT", "default": "Montserrat", "description": "Font used by web editor"}, {"name": "WE_FONT_WEIGHT", "default": ":200,300,400,500,600,700,800,900", "description": "Font weight used by web editor"}, {"name": "WE_UNAUTH_BG", "example": "/media/unauth-bg.png", "userInput": true, "description": "Location of background when user is not authenticated"}, {"name": "WE_RECAPTCHA_KEY", "default": "6LcG200nAAAAAPlfUeUKgk8JEm1cRYbm7PIykoTy", "description": "Key used by RECAPTCHA"}, {"name": "WE_HS_ENABLED", "default": "${HS_ENABLED}", "description": "If health service is enabled"}, {"name": "WE_DS_ENABLED", "default": "${DS_ENABLED}", "description": "If disk service is enabled"}, {"name": "WE_ES_ENABLED", "default": "${ES_ENABLED}", "description": "If event service is enabled"}, {"name": "WE_CS_ENABLED", "default": "${CM_ENABLED}", "description": "If configuration manager is enabled"}, {"name": "WE_TDA_ENABLED", "default": "${TDA_ENABLED}", "description": "If timescale data access is enabled"}, {"name": "WE_PRIMARY_COLOR", "default": "${GENERAL_PRIMARY_COLOR}", "description": "Web-editor primary color"}, {"name": "WE_SECONDARY_COLOR", "example": "110 193 228", "userInput": true, "description": "Web-editor secondary color"}, {"name": "WE_PRIMARY_HOVER_COLOR", "example": "30 58 138", "userInput": true, "description": "Web-editor primary color when hovering"}, {"name": "WE_ERROR_COLOR", "default": "162 32 51", "description": "Web-editor error color"}, {"name": "WE_WARNING_COLOR", "default": "235 133 39", "description": "Web-editor warning color"}, {"name": "WE_SUCCESS_COLOR", "default": "21 162 52", "description": "Web-editor success color"}, {"name": "WE_CONTRAST_COLOR", "default": "255 255 255", "description": "Web-editor contrast color"}, {"name": "WE_PRIMARY_THIN_COLOR", "default": "229 231 235", "description": "Web-editor thin color"}, {"name": "WE_PRIMARY_NOT_ACTIVE", "example": "171 171 186", "userInput": true, "description": "Web-editor not active color"}, {"name": "WE_GOOGLE_MAP_KEY", "default": "AIzaSyAamWnxuYV67O2bkueDftNCGHS5Wpqn4o8", "description": "Web-editor map key"}, {"name": "WE_MAP_COLOR", "default": "131246", "description": "Web-editor map color"}, {"name": "WE_GOOGLE_MAP_DEFAULT_LAT", "default": "45.394439884270454", "description": "Web-editor default latitude"}, {"name": "WE_GOOGLE_MAP_DEFAULT_LNG", "default": "11.021467598411013", "description": "Web-editor default longitude"}, {"name": "WE_BOXIO_MANAGER_VERSION", "default": "${BOXIO_MANAGER_VERSION}", "description": "Reference to boxio manager version"}, {"name": "WE_CONF_SERVICE_VERSION", "default": "${CONFIGURATION_MANAGER_VERSION}", "description": "Reference to configuration service version"}, {"name": "WE_TDA_SERVICE_VERSION", "default": "${TIMESCALE_DATA_ACCESS_VERSION}", "description": "Reference to timescale data access version"}, {"name": "WE_EVENT_SERVICE_VERSION", "default": "${EVENT_MANAGER_VERSION}", "description": "Reference to event service version"}, {"name": "WE_HEALTH_SERVICE_VERSION", "default": "${HEALTH_SERVICE_VERSION}", "description": "Reference to health service version"}, {"name": "WE_JITSI_SERVICE_VERSION", "default": "${JITSI_VERSION}", "description": "Reference to jitsi service version"}, {"name": "WE_CALL_SERVICE_VERSION", "default": "${CALL_SERVICE_VERSION}", "description": "Reference to call service version"}, {"name": "WE_DISK_SIZE_SERVICE_VERSION", "default": "${DISK_SIZE_VERSION}", "description": "Reference to disk service version"}, {"name": "WE_STORM_VERSION", "default": "${STORM_VERSION}", "description": "Reference to storm service version"}, {"name": "WE_EMQX_VERSION", "default": "${MQTT_VERSION}", "description": "Reference to mqtt service version"}, {"name": "WE_RABBITMQ_VERSION", "default": "${RABBITMQ_VERSION}", "description": "Reference to rabbitmq service version"}, {"name": "WE_PSQL_VERSION", "default": "${AUTHDB_VERSION}", "description": "Reference to database service version"}, {"name": "WE_MONGODB_VERSION", "default": "${MONGO_VERSION}", "description": "Reference to mongodb service version"}, {"name": "WE_COUCHDB_VERSION", "default": "${COUCHDB_VERSION}", "description": "Reference to couchdb service version"}, {"name": "WE_ENDPOINT_JSON_VERSION", "default": "${ENDPOINT_JSON_VERSION}", "description": "Reference to endpoint.json file version"}, {"name": "WE_ETENSIL_TPU_IMAGE", "default": "/media/etensil.jpeg", "description": "Etensil tpu image"}, {"name": "WE_MAP_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If map visualization in web-editor is enabled"}, {"name": "WE_ZONE_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If zone visualization in web-editor is enabled"}, {"name": "WE_MOBILE_SETTINGS_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If mobile settings visualization in web-editor is enabled"}, {"name": "WE_DISK_SPACE_ENABLED", "enum": ["true", "false"], "userInput": true, "description": "If installation disk space visualization in web-editor is enabled"}, {"name": "WE_GETSTREAMIO_API_KEY", "userInput": true, "description": "???"}]}]