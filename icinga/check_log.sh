#!/bin/bash 

#
# Check if the specified log prints specified string, both parameters passed in input
# check_interval is passed by the Service object which calls the check
#
# ./check_log.sh -l logName -s string -i check_interval



print_help() {
	echo "Check if the specified log prints specified string."
	echo "Usage:"
	echo "[-l] log name"
	echo "[-s] string to find"
	echo "[-i] check_interval"
	exit 0
}


#take arguments in input

while test -n "$1"; do
	case "$1" in
		--help|-h)
			print_help
			exit 0
			;;
		-l)
			log=$2
			shift 
			;;
		-s)
			string=$2
			shift
			;;
		-i)
			interval=$2
			shift
			;;
		*)
			echo "Unknown option: $1"
			print_help
			exit 3 
			;;
	esac 
	shift
done

#check if arguments are empty
if [ "$log" == "" ]; then
	echo "No log specified"
	print_help
	exit 3;
fi

if [ "$string" == "" ]; then
	echo "No string specified"
	print_help
	exit 3;
fi

if [ "$interval" == "" ]; then
	echo "No check interval specified"
	print_help
	exit 3;
fi

#TIP: duration literals(1m, 2d1m...) used in Icinga are converted to seconds by the config parser and are treated like numeric literals

#check if string has been written on log between the current check and the last check

strings=$(journalctl -u $log --since "$interval seconds ago" | grep $string)

#check if string is present
if test -z "$strings" 
then
	echo "OK. $string not present in $log"
	exit 0;
else
	echo "CRITICAL. $string present in $log. In particular:
	$strings"
	exit 2;
fi
