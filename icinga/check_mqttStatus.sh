#!/bin/bash

#
# check mqtt status
#
# ./check_mqttStatus.sh



print_help() {
	echo "Check mqtt status"
	echo "No arguments needed."
	exit 0
}


#take arguments in input

while test -n "$1"; do
	case "$1" in
		--help|-h)
			print_help
			exit 0
			;;
		*)
			echo "Unknown option: $1"
			print_help
			exit 3
			;;
	esac
	shift
done


token=$(curl -s -k -d '{"client_secret": "1d737b2a-b4db-11ec-b909-0242ac120002", "grant_type": "client_credentials",  "client_id": "32e49124-b4db-11ec-b909-0242ac120002"}' -H 'Content-Type: application/json' https://127.0.0.1:9997/uaa/oauth/token | jq -r '.access_token')

conf=$(curl -s -k -H "Authorization: Bearer $token" https://127.0.0.1:9999/getConfigurations)

ids=$(echo $conf | jq -r '.[] | .id')
macs=$(echo $conf |jq -r '.[] | .mac_address')

declare -a mqtt_disconnected
index=0
iteration=1

while IFS= read -r line;
do
   id=$line
   mac=$(echo "$macs" | sed -n "$iteration"p)

   iteration=$((iteration + 1))

   mqttStatus=$(curl -s -k -H "Authorization: Bearer $token" -d '{"mac":"'"$mac"'"}' -H 'Content-Type: application/json' https://127.0.0.1:9998/getConnectionStatus/$id | jq -r '.queryResult[].connected')


   if [[ $mqttStatus == "false" ]]; then
      mqtt_disconnected[index]=$mac

      index=$((index + 1))

   fi;
done <<< "$ids".


if [ -z "$mqtt_disconnected" ]; then
        echo "OK. Mqtt is connected on all nodes"
        exit 0;
else
        echo "ERROR. Mqtt is disconnected on some nodes."
        echo "Mqtt is disconnected on the following nodes:"
        echo "${mqtt_disconnected[*]}" | tr " " "\n"
        exit 2;
fi
