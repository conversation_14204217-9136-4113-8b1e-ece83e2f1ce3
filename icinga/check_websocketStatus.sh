#!/bin/bash

#
# check websocket status
#
# ./check_websocket.sh



print_help() {
	echo "Check websocket status"
	echo "No arguments needed."
	exit 0
}


#take arguments in input

while test -n "$1"; do
	case "$1" in
		--help|-h)
			print_help
			exit 0
			;;
		*)
			echo "Unknown option: $1"
			print_help
			exit 3
			;;
	esac
	shift
done

token=$(curl -s -k -d '{"client_secret": "1d737b2a-b4db-11ec-b909-0242ac120002", "grant_type": "client_credentials",  "client_id": "32e49124-b4db-11ec-b909-0242ac120002"}' -H 'Content-Type: application/json' https://127.0.0.1:/am/oauth/token | jq -r '.access_token')

conf=$(curl -s -k -H "Authorization: Bearer $token" https://127.0.0.1/am/configuration/info/list)
echo $(curl -s -k -H "Authorization: Bearer $token" https://127.0.0.1/am/configuration/info/list)
statusAndMAC=$(echo $conf | jq -r '.[] | .configuration_service_status + " " + .mac_address')

declare -a mac_unreach
index=0

while IFS= read -r line;
do
   if [[ $line == *"unreachable"* ]]; then
      mac_unreach[index]=$(echo $line | cut -d' ' -f2)
      index=$((index + 1))
   fi;
done <<< "$statusAndMAC"


if [ -z "$mac_unreach" ]; then
        echo "OK. Websocket working on all nodes"
        exit 0;
else
        echo "ERROR. Websocket not working on some nodes"
        echo "Websocket not working on the following nodes:"
        echo "${mac_unreach[*]}" |  tr " " "\n"
        exit 2;
fi
