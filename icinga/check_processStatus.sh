#!/bin/bash

#
# Check status and if it's enabled on boot of specified process
# Check if there has been a pid change and issue a warning if so
#
# ./check_processStatus.sh -p processName


print_help() {
    echo "Check status and if it's enabled on boot of specified process."
    echo "Check if there has been a pid change and issue a warning if so."
    echo "Usage:"
    echo "[-p] process name"
    exit 0
}


#take arguments in input

while test -n "$1"; do
    case "$1" in
        --help|-h)
            print_help
            exit 0
            ;;
        -p)
            process=$2
            shift
            ;;
        *)
            echo "Unknown option: $1"
            print_help
            exit 3
            ;;
    esac
    shift
done


#check if arguments are empty

if [ "$process" == "" ]; then
    echo "No process specified"
    print_help
    exit 3;
fi

#check status, boot option and description of process

status=$(docker ps --format "{{.Names}} {{.Status}}" | grep $process | awk '{print $2}')

if [ "$status" = "Up" ]; then
    echo "OK."
else
    echo "ERROR."
    echo "$(docker ps --format "{{.Names}} {{.Status}}" | grep $process)"
    exit 2;
fi
