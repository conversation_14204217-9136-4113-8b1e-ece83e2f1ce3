#!/usr/bin/python3
import os
import subprocess

# Set HOME environment variable
os.environ['HOME'] = '/var/lib/nagios'

# Function to run a command and check its result
def run_command(command):
    result = subprocess.run(command, capture_output=True, text=True)
    return result.returncode, result.stdout, result.stderr

# Check EMQX status
emqx_status_command = ['docker', 'exec', 'mqtt', 'emqx_ctl', 'status']
status_code, _, _ = run_command(emqx_status_command)

if status_code != 0:
    print("emqx does not respond")
    exit(2)

# Check for specific client ID in the list of clients
client_id = "6ZvV6CxOJ0C_CdIkwsaZ0w"
client_check_command = ['docker', 'exec', 'mqtt', 'emqx_ctl', 'clients', 'list']
status_code, stdout, _ = run_command(client_check_command)

if client_id in stdout:
    print("Storm works properly.")
    exit(0)
else:
    print("Storm does not work correctly.")
    exit(1)
