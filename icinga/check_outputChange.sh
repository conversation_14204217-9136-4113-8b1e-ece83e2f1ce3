#!/bin/bash

# Store the previous output in a temp file
PREV_OUTPUT_FILE="/var/tmp/icinga_service_output.txt"

# Get the current output from the check command
CURRENT_OUTPUT=$(/usr/lib/nagios/plugins/software_versions.sh)

# Check if the previous output file exists
if [[ -f $PREV_OUTPUT_FILE ]]; then
    PREV_OUTPUT=$(cat $PREV_OUTPUT_FILE)
else
    PREV_OUTPUT=""
fi

# Compare current and previous output
if [[ "$CURRENT_OUTPUT" != "$PREV_OUTPUT" ]]; then
    echo "Output has changed. Triggering notification."
    echo "$CURRENT_OUTPUT" >"$PREV_OUTPUT_FILE"
    exit 1 # Change status to trigger notification
else
    echo "Output is the same."
    exit 0 # Status unchanged
fi
