#!/bin/bash

UPTIME=$(uptime)
RAM_TOTAL=$(free -h | grep Mem: | awk '{print $2}')
RAM_USED=$(free -h | grep Mem: | awk '{print $3}')
RAM_FREE=$(free -h | grep Mem: | awk '{print $4}')
RAM_AVAILABLE=$(free -h | grep Mem: | awk '{print $7}')
CPU_NUMBER=$(iostat -c | head -n1 | awk '{print$6 $7}' | cut -d '(' -f2 | cut -d ')' -f1)
CPU_USER=$(iostat -c | grep -v "avg-cpu" | grep -v "Linux" | sed -n 2p | awk '{print$1}')
CPU_NICE=$(iostat -c | grep -v "avg-cpu" | grep -v "Linux" | sed -n 2p | awk '{print$2}')
CPU_SYSTEM=$(iostat -c | grep -v "avg-cpu" | grep -v "Linux" | sed -n 2p | awk '{print$3}')
CPU_IOWAIT=$(iostat -c | grep -v "avg-cpu" | grep -v "Linux" | sed -n 2p | awk '{print$4}')
CPU_STEAL=$(iostat -c | grep -v "avg-cpu" | grep -v "Linux" | sed -n 2p | awk '{print$5}')
CPU_IDLE=$(iostat -c | grep -v "avg-cpu" | grep -v "Linux" | sed -n 2p | awk '{print$6}')
SO_DISK_TOTAL=$(df -h | grep /dev/ | grep -v tmpfs | grep -v mapper | awk '{print$2}')
SO_DISK_USED=$(df -h | grep /dev/ | grep -v tmpfs | grep -v mapper | awk '{print$3}')
SO_DISK_AVAILABLE=$(df -h | grep /dev/ | grep -v tmpfs | grep -v mapper | awk '{print$4}')
SO_DISK_USE_PERC=$(df -h | grep /dev/ | grep -v tmpfs | grep -v mapper | awk '{print$5}')
DATA_DISK_TOTAL=$(df -h | grep /dev/ | grep -v tmpfs | grep mapper | awk '{print$2}')
DATA_DISK_USED=$(df -h | grep /dev/ | grep -v tmpfs | grep mapper | awk '{print$3}')
DATA_DISK_AVAILABLE=$(df -h | grep /dev/ | grep -v tmpfs | grep mapper | awk '{print$4}')
DATA_DISK_USE_PERC=$(df -h | grep /dev/ | grep -v tmpfs | grep mapper | awk '{print$5}')



RAM_TOTAL=${RAM_TOTAL%G}
SO_DISK_TOTAL=${SO_DISK_TOTAL%G}
DATA_DISK_TOTAL=${DATA_DISK_TOTAL%G}



echo "SERVER LOAD:"

#generic information + performance data for each info
echo "UPTIME = $UPTIME"
echo "NUMBER OF CPUs = $CPU_NUMBER | 'AVG-CPU %user'=${CPU_USER}% 'AVG-CPU %nice'=${CPU_NICE}% 'AVG-CPU %system'=${CPU_SYSTEM}% 'AVG-CPU %iowait'=${CPU_IOWAIT}% 'AVG-CPU %steal'=${CPU_STEAL}% 'AVG-CPU %idle'=${CPU_IDLE}%"
echo "TOTAL RAM = ${RAM_TOTAL}GB | 'RAM used'=${RAM_USED}B 'RAM free'=${RAM_FREE}B 'RAM available'=${RAM_AVAILABLE}B"
echo "TOTAL OPERATING SYSTEM DISK = ${SO_DISK_TOTAL}GB | DISK used=${SO_DISK_USED}B DISK available=${SO_DISK_AVAILABLE}B"


if [ ! -z "$DATA_DISK_TOTAL" ]; then
    echo -e "{DATA DISK: $DATA_DISK_TOTAL}GB | 'DATA DISK used'=${DATA_DISK_USED}B 'DATA DISK available'=${DATA_DISK_AVAILABLE}B"
fi


exit 0;
