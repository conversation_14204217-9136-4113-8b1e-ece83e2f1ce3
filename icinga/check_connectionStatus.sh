#!/bin/bash

#
# check connection status
#
# ./check_connectionStatus.sh


print_help() {
	echo "Check connection status"
	echo "No arguments needed."
	exit 0
}


#take arguments in input

while test -n "$1"; do
	case "$1" in
		--help|-h)
			print_help
			exit 0
			;;
		*)
			echo "Unknown option: $1"
			print_help
			exit 3
			;;
	esac
	shift
done


token=$(curl -s -k -d '{"client_secret": "1d737b2a-b4db-11ec-b909-0242ac120002", "grant_type": "client_credentials",  "client_id": "32e49124-b4db-11ec-b909-0242ac120002"}' -H 'Content-Type: application/json' https://127.0.0.1:9997/uaa/oauth/token | jq -r '.access_token')

conf=$(curl -s -k -H "Authorization: Bearer $token" https://127.0.0.1:9999/getConfigurations)
ids=$(echo $conf | jq -r '.[] | .id')
jsonArrayIds=$(echo $ids | tr " " ",")
retrieveConn=$(curl -s -k -H "Authorization: Bearer $token" -d '{"installations":['$jsonArrayIds']}' -H 'Content-Type: application/json' https://127.0.0.1:9998/retrieveConnectionCounters)
connSeparateLines=$(echo $retrieveConn | jq -c '.[]')
echo "Connection status of all endpoints:"
while IFS= read -r line;
do
   id=$(echo $line | jq -r '.installation')
   echo "Installation = '$id'"

   areas=$(echo $line | jq -r '.endpoints | group_by('.area')')
   areasSeparateLines=$(echo $areas | jq -c '.[]')

   while IFS= read -r lineArea;
   do
      area=$(echo $lineArea | jq -r '.[0].area')
      echo "Area = '$area'"

      partitions=$(echo $lineArea | jq -r 'group_by('.partition')')
      partitionsSeparateLines=$(echo $partitions | jq -c '.[]')

      while IFS= read -r linePartition;
      do
         partition=$(echo $linePartition | jq -r '.[0].partition')
         echo "Partition = '$partition'"

         names=$(echo $linePartition | jq '.[].name')
         connectivity=$(echo $linePartition | jq -r '.[].connectivity')

         eval "arrayNames=($names)"
         eval "arrayConn=($connectivity)"
         for name in "${!arrayNames[@]}";
         do
            echo -e "\t- endpoint: '${arrayNames[$name]}', status: '${arrayConn[$name]}'";
         done


      done <<< "$partitionsSeparateLines"

   done <<< "$areasSeparateLines"

   echo ""
done <<< "$connSeparateLines"
