#!/bin/bash

export HOME=/var/lib/nagios
mapfile -t boxios < <( docker exec mqtt emqx_ctl clients list )
nBoxios=0

for row in "${boxios[@]}"
do
  MAC=$(echo $row | awk '{print $1}' | cut -f2 -d"(" | cut -f1 -d"," | sed -n "/^\([0-9a-z][0-9a-z]:\)\{5\}[0-9a-z][0-9a-z]$/p")
  DATE=$(echo $row | awk '{print $5}' | cut -f2 -d"=" | cut -f1 -d")")
  IP=$(echo $row | awk '{print $4}' | cut -f2 -d"=" | cut -f1 -d":")
  if [ ! -z $MAC ]; then
    echo "BOXIO: $MAC connected at $(date -d@${DATE}) from IP $IP"
    nBoxios=$((nBoxios+1))
  fi
done

if [[ $nBoxios -eq 0 ]]; then
   echo "No BOXIOs connected"
fi


exit 0;
