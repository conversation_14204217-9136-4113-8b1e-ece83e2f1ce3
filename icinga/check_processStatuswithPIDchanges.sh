#!/bin/bash

#
# Check status and if it's enabled on boot of specified process
# Check if there has been a pid change and issue a warning if so
#
# ./check_processStatuswithPIDchanges.sh -p processName


print_help() {
    echo "Check status and if it's enabled on boot of specified process."
    echo "Check if there has been a pid change and issue a warning if so."
    echo "Usage:"
    echo "[-p] process name"
    exit 0
}


#take arguments in input

while test -n "$1"; do
    case "$1" in
        --help|-h)
            print_help
            exit 0
            ;;
        -p)
            process=$2
            shift
            ;;
        *)
            echo "Unknown option: $1"
            print_help
            exit 3
            ;;
    esac
    shift
done


#check if arguments are empty

if [ "$process" == "" ]; then
    echo "No process specified"
    print_help
    exit 3;
fi

#check status, boot option and description of process


#filename and path of the file where recent PIDs are written
logFile="/var/log/check_processStatus/processPIDchanges.log"
logPath="/var/log/check_processStatus"
logFilename="processPIDchanges.log"

#get only the first PID, that corresponds to the Main PID of systemctl status process
PID=$(ps ax | grep -i $process | grep -v grep | grep -v bash | awk 'NR==1{print $1}')
exists="false"
new="true" #remains true if the process wasn't monitored before and there is no existing entry regarding the process in the logFile
#no entry could be one reason of exists for being false, we don't want a warning for that

#read every line of log file and see if the previous pid written in the logfile is equal to current pid
while IFS= read -r line
do
    processName=$(echo $line | awk '{print $1}')
    if [ "$processName" = "$process" ]; then
        prevPID=$(echo $line | awk '{print $2}')
	new="false"
	if [ "$prevPID" = "$PID" ]; then
            exists="true"
	fi
    fi
done < "$logFile"

#write on the log the name of the process and the current pid

#delete previous entry
cd $logPath
sed -i '/'"$process"'/d' $logFilename

if [[ ! $PID -eq "" ]]; then
        #write new entry
        echo $process " "  $PID >> $logFile
fi


statusPrev="UNKNOWN"
#checks status and PID changes
status=$(docker ps --format "{{.Names}} {{.Status}}" | grep $process | awk '{print $2}')
if [ "$status" = "Up" ]; then
    echo "OK."
    echo "Status=$status"
    statusPrev="OK"
else
    echo "ERROR."
    echo "Status=$status"
    statusPrev="NOT OK"
fi

if  [ "$exists" = "false" ] && [ "$new" = "false" ]; then
    echo "WARNING: PID for $process changed"
    if [ "$statusPrev" = "OK" ]; then #from the check before it was all ok
	exit 1;
    elif [ "$statusPrev" = "NOT OK" ]; then #from the check before there was an error
	exit 2;
    fi
elif [ "$statusPrev" = "OK" ]; then #from the check before it was all ok
	exit 0;
elif [ "$statusPrev" = "NOT OK" ]; then #from the check before there was an error
	exit 2;
else
    echo "WARNING."
    echo "docker ps | grep $process"
    exit 1;
fi
