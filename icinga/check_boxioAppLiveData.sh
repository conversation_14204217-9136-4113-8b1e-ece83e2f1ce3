#!/bin/bash

#check from Monit


export HOME=/var/lib/nagios
# checks emqx status
docker exec mqtt emqx_ctl status > /dev/null
RESULT=$(echo $?)


# restart emqx
if [[ $RESULT -eq 1 ]]; then
	echo "todo verify if we want to restart mqtt"        # docker restart mqtt
fi


# checks emqx status again
docker exec mqtt emqx_ctl status > /dev/null
statusEmqx=$(echo $?)


# checks status of web dashboard
docker exec mqtt emqx_ctl  clients list | grep boxioApp > /dev/null
webDashboard=$(echo $?)


# if emqx still doesn't work, there is an error
if [[ $statusEmqx -eq 1 ]]; then
        echo "ERROR. emqx does not respond"
        exit 2;
elif [[ $webDashboard -eq 0 ]]; then # web dashboard working
        echo "Web Dashboard live data available"
        exit 0;
elif [[ $webDashboard -eq 1 ]]; then # a restart of tomcat 8 is required and web dashboard is not working
        echo "ERROR. Web Dashboard live data not available. Trying to restart tomcat8..."
        docker restart web-dashboard
        exit 2;
else  # web dashboard not going
        echo "ERROR. Web Dashboard live data not available; try to restart process."
        exit 2;
fi
