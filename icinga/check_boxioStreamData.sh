#!/bin/bash

#check from Monit


export HOME=/var/lib/nagios
docker exec mqtt emqx_ctl status >/dev/null
RESULT=$(echo $?)

if [[ $RESULT -eq 1 ]]; then
   echo "emqx does not respond"
   exit 2;
fi

docker exec mqtt emqx_ctl clients list | grep storm >/dev/null
RESULT=$(echo $?)

if [[ $RESULT -eq 0 ]]; then
   echo "Storm works properly."
   exit 0;
else
   echo "Storm does not work correctly."
fi

exit $RESULT
