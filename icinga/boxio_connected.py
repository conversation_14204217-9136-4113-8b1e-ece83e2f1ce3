#!/usr/bin/python3
import os
import subprocess
import re
from datetime import datetime

# Set HOME environment variable
os.environ['HOME'] = '/var/lib/nagios'

# Run the Docker command and capture the output
result = subprocess.run(
    ['docker', 'exec', 'mqtt', 'emqx_ctl', 'clients', 'list'],
    capture_output=True,
    text=True
)

# Parse the output into lines
boxios = result.stdout.splitlines()

n_boxios = 0

# Regular expression for UUID
uuid_pattern = re.compile(r'[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', re.IGNORECASE)

for row in boxios:
    # Extract UUID
    uuid_search = re.search(uuid_pattern, row)
    uuid = uuid_search.group(0) if uuid_search else None

    # Extract DATE
    date_search = re.search(r'connected_at=([0-9]+)', row)
    date = int(date_search.group(1)) if date_search else None

    # Extract IP
    ip_search = re.search(r'ip=([^:]+)', row)
    ip = ip_search.group(1) if ip_search else None

    if uuid:
        date_formatted = datetime.fromtimestamp(date/1000).strftime('%Y-%m-%d %H:%M:%S') if date else "unknown"
        print(f"BOXIO: {uuid} connected at {date_formatted}")
        n_boxios += 1

# If no BOXIOs were connected, print a message
if n_boxios == 0:
    print("No BOXIOs connected")

exit(0)

