#!/usr/bin/python3

import argparse
import requests
import sys
import subprocess
import json

# Configuration
repo_base = "registry.edalab.it/edalab"
gitlab_api_url = "https://git.edalab.it/api/v4"
upper_folder = "/opt/boxio/"


def get_project_id(project_path, gitlab_token):
    """Get the project ID from GitLab API using the project path."""
    try:
        repo_project_path = project_path.split("/")[-1]
        headers = {"Private-Token": gitlab_token}
        response = requests.get(f"{gitlab_api_url}/projects?search={repo_project_path}", headers=headers)
        if response.status_code != 200:
            print(f"UNKNOWN - Error retrieving project ID from GitLab: HTTP {response.status_code}")
            sys.exit(3)  # Unknown status

        projects = response.json()
        for project in projects:
            path_parts = project_path.split("/")
            search_project_path = "/".join(path_parts[1:])
            if project["path_with_namespace"] == search_project_path:
                return project["id"]

        print(f"UNKNOWN - Project with path '{repo_project_path}' not found.")
        sys.exit(3)

    except requests.RequestException as e:
        print(f"UNKNOWN - Error retrieving project ID from GitLab: {e}")
        sys.exit(3)  # Unknown status


def get_running_images():
    """Get all Docker images currently running on the host that match the base repository pattern."""
    try:
        result = subprocess.run(
            ["docker", "ps", "--format", "{{.Image}}"],
            capture_output=True,
            text=True,
            check=True,
        )
        images = result.stdout.strip().split("\n")
        return [img for img in images if img.startswith(repo_base)]
    except subprocess.CalledProcessError as e:
        print(f"UNKNOWN - Error retrieving running Docker images: {e}")
        sys.exit(3)  # Unknown status


def compare_file(gitlab_token, file):
    """Get the latest version for file both on repo and on machine"""
    try:
        headers = {"Private-Token": gitlab_token}
        version = "0"
        url = gitlab_api_url + "/projects/%s/releases/" % (file["project_id"])
        response = requests.request("GET", url, headers=headers, verify=True)
        releases = response.json()
        latest_release_name = releases[0]["name"]
        with open(upper_folder + file["executive_path"], "r", encoding="utf-8") as f:
            data = json.load(f)
            version = data["version"]
        return version, latest_release_name
    except requests.RequestException as e:
        print(f"UNKNOWN - Error retrieving tags from GitLab: {e}")
        sys.exit(3)  # Unknown status


def get_latest_tag_for_branch(project_id, branch_name, gitlab_token):
    """Get the latest tagged commit on the specified branch using GitLab API."""
    try:
        headers = {"Private-Token": gitlab_token}

        # Get the list of all tags from the GitLab API
        tags_response = requests.get(
            f"{gitlab_api_url}/projects/{project_id}/repository/tags?page=1&per_page=100",
            headers=headers,
        )

        if tags_response.status_code != 200:
            print(f"UNKNOWN - Error retrieving tags from GitLab: HTTP {tags_response.status_code}")
            sys.exit(3)  # Unknown status

        tags = tags_response.json()

        # Get the list of commits for the branch
        branch_commits_response = requests.get(
            f"{gitlab_api_url}/projects/{project_id}/repository/commits?ref_name={branch_name}&page=1&per_page=100",
            headers=headers,
        )

        if branch_commits_response.status_code != 200:
            print(f"UNKNOWN - Error retrieving branch commits from GitLab: HTTP {branch_commits_response.status_code}")
            sys.exit(3)  # Unknown status

        branch_commits = branch_commits_response.json()

        # Find the latest commit on the branch that has an associated tag
        for commit in branch_commits:
            for tag in tags:
                if commit["id"] == tag["commit"]["id"]:
                    return tag["name"]

        return None  # No tagged commit found on the branch

    except requests.RequestException as e:
        print(f"UNKNOWN - Error retrieving tags from GitLab: {e}")
        sys.exit(3)  # Unknown status


def extract_project_path(image_name):
    """Extract the project path from a Docker image name."""
    try:
        # Example image_name: registry.edalab.it/edalab/boxio/gui/monorepo-gui/web-editor:3.0.0-rc02
        # Extract: registry.edalab.it/edalab/boxio/gui/monorepo-gui
        path_parts = image_name.split("/")
        project_path = "/".join(path_parts[:-1])  # Join parts to form the project path
        return project_path
    except IndexError:
        print(f"UNKNOWN - Unable to extract project path from image: {image_name}")
        sys.exit(3)  # Unknown status


def main():
    # Argument parsing
    parser = argparse.ArgumentParser(description="Check Docker service versions.")
    parser.add_argument("--gitlab-token", required=True, help="GitLab API token for authentication")
    args = parser.parse_args()
    GITLAB_TOKEN = args.gitlab_token
    # Determine the branch to use based on the machine type
    with open("/opt/boxio/release/config_file.json", "r", encoding="utf-8") as file:
        data = json.load(file)
    container_branch_map = {service["container"]: service["branch"] for service in data["services"]}

    # Get all relevant running images
    running_images = get_running_images()
    status = 0
    error_list = []
    for file in data["external_files"]:
        version, latest_release_name = compare_file(GITLAB_TOKEN, file)
        if latest_release_name not in version:
            error_list.append(
                f"CRITICAL - endpoint.json is running version {version}, but the latest version is {latest_release_name}."
            )
            if status < 2:
                status = 2
        else:
            print(f"OK - endpoint.json is already running the latest version ({latest_release_name}).")

    for image in running_images:
        running_version = image.split(":")[-1]
        service_name = image.split("/")[-2]
        repo_project_path = extract_project_path(image)
        # Get the project ID using the GitLab API
        project_id = get_project_id(repo_project_path, GITLAB_TOKEN)
        # Get the latest tag for the determined branch using GitLab API
        branch = container_branch_map[service_name]
        if branch is None:
            continue
        latest_tag = get_latest_tag_for_branch(project_id, branch, GITLAB_TOKEN)

        if not latest_tag:
            status = 3
            error_list.append(f"UNKNOWN - No tags found for branch: {branch} in {service_name}")
            if status < 3:
                status = 3
            continue
        # Compare versions
        if running_version == latest_tag or running_version in latest_tag:
            error_list.append(f"OK - {service_name} is running the latest version ({running_version}).")
        else:
            error_list.append(
                f"CRITICAL - {service_name} is running version {running_version}, but the latest version is {latest_tag}."
            )
            if status < 2:
                status = 2
    for error in error_list:
        print(error)
    sys.exit(status)  # OK


if __name__ == "__main__":
    main()
