
# Scripts for server

#### boxio_connected.sh
_Goal_: check if there are BOXIOs connected to the server

_Use_: ./boxio_connected.sh

The check reads from **/opt/emqttd/bin/emqttd_ctl clients list** the list of clients for emqttd. For every row, the MAC address, the date from when the client has been connected and the IP address are memorized. If the MAC address is set, then the row refers to  a connected BOXIO.

### ram_services.sh
_Goal_: monitor the RAM usage of the following services: auth-manager, configuration-manager, couchdb, emqttd, postgres, rabbitmq, timescale-data-access, tomcat8 and storm.

_Use_: ./ram_services.sh

The check constructs an array of PIDs of every service and then checks the RAM consumption. The output is shown on the Icinga2 web dashboard as performance data, with the name of the service and the amount of RAM consumed by the service.

### server_load.sh
_Goal_: check the server load

_Use_: ./server_load.sh

The check shows the resources’ (RAM, CPU, DISK) status. In particular, this script returns the following output:
- _UPTIME =  <date> <living days>, <living hours>, load average_, where load average is the average number of processes that are either in a runnable or uninterruptible state.  A process in a runnable state is either using the CPU or waiting to use the CPU.  A process  in an uninterruptible  state  is  waiting for some I/O access, e.g. waiting for disk.  The averages are taken over the three time intervals.  Load averages are not normalized for the number of CPUs in a system, so a load average of 1 means a single CPU system is loaded all the time while on a 4 CPU system it means it was idle 75% of the time.
- _NUMBER OF CPUs = <number>CPU_
- _TOTAL RAM = <total ram>GB_
- _TOTAL OPERATING SYSTEM DISK = <total disk>GB_

The script also returns some performance data. In particular:
- _AVG-CPU %idle = <percentage>%_: shows the percentage of time that the CPU or CPUs were idle and the system did not have an outstanding disk I/O request.
- _AVG-CPU %sistem = <percentage>%_: shows the percentage of CPU utilization that occurred while executing at the system level (kernel).
- _AVG-CPU % user  = <percentage>%_: shows the percentage of CPU utilization that occurred while executing at the user level (application)
- _AVG-CPU %steal  = <percentage>%_: shows the percentage of time spent in involuntary wait by the virtual CPU or CPUs while the hypervisor was servicing another virtual processor.
- _AVG-CPU %nice = <percentage>%_: shows the percentage of CPU utilization that occurred while executing at the user level with nice priority
- _AVG-CPU %iowait = <percentage>%_: shows the percentage of time that the CPU or CPUs were idle during which the system had an outstanding disk I/O request.
- _RAM used = <ram>GB_
- _RAM free = <ram>GB_
- _RAM available = <ram>GB_
- _DISK used = <ram>GB_
- _DISK available = <ram> GB_

### software_versions.sh
_Goal_: check software versions of every remote server software

_Use_: ./software_version.sh

The check returns the version of couchdb, postgres, rabbitmq, erlang, emqttd, apache storm, zookeeper, auth-manager, configuration-manager, timescale-data-access, storm topology, web dashboard.


# Scripts for server - monit

### check_boxioAppLiveData.sh
_Goal_: check web dashboard’s status

_Use_: ./check_boxioAppLiveData

First of all, the check controls emqttd’s status. If it does not respond, then emqttd is restarted. The status of emqttd is then checked again to make sure that it is really working and it is memorized in a variable.
Then the actual monitoring of boxioApp starts: it is checked if boxioApp is a client of emqttd.
If emqttd is not working, there’s an error. If emqttd and the web dashboard are working,, there are no issues. If emqttd is working but the web dashboard is not, an error is issued.

### check_boxioStreamData.sh
_Goal_: check storm’s status

_Use_: ./check_boxioStreamData.sh

First of all, the check controls emqttd’s status. If it does not respond, then an error is issued, otherwise the check continues. Then it is checked if storm is a client of emqttd. If it is, then storm is working correctly, otherwise an error is issued.

### check_couchdb.sh
_Goal_: check couchdb’s status

_Use_: ./check_couchdb.sh

The check uses **systemctl show couchdb --property=ActiveState** command to check the status of couchdb. Then, the check controls if port 6984 is open with the command **netstat** and if that port is associated with couchdb. Lastly, it prints the content of https://localhost:6984 with the command **curl** to check if it contains the string “couchdb”.
If the status is inactive, failed or error, or the port is not open or it is not working with couchdb, or if the website is not associated with couchdb, an error is returned; if the status of couchdb is unknown, a warning is returned. Otherwise everything is working correctly.

### check_emqttd.sh
_Goal_: check emqttd’s status

_Use_: ./check_emqttd.sh

The check uses **systemctl show emqttd --property=ActiveState** command to check the status of emqttd. Then, the check controls if port 8883 is open with the command **netstat** and if that port is associated with emqttd.
If the status is inactive, failed or error, or the port is not open or it is not working with emqttd, an error is returned; if the status of emqttd is unknown, a warning is returned. Otherwise everything is working correctly.

### check_tomcat8.sh
_Goal_: check tomcat8’s  status

_Use_: ./check_tomcat8.sh

The check uses **systemctl show tomcat8 --property=ActiveState** command to check the status of tomcat8. Then, the check controls if port 443 is open with the command **netstat** and if that port is associated with tomcat8.
If the status is inactive, failed or error, or the port is not open or it is not working with tomcat8, an error is returned; if the status of tomcat8 is unknown, a warning is returned. Otherwise everything is working correctly.

### other checks
The checks on auth-manager’s, configuration-manager’s, postgres’s, rabbitmq’s, timescale-data-access’s statuses are done with the script **_check_processStatus.sh_** from the directory “box”.

The check on the disk of the server is done by using the command **_disk_**, contained in the ITL (Icinga Template Library) with the options _disk_wfree=25%_ and _disk_cfree=10%_ and -disk_partitions = the partitions of the server that is being monitored_.

The check on the ram of the server is done by the following check found on Icinga Exchange: https://github.com/ozzi-/check_ram with _crf_critical = 90_ and _crf_warning = 70_.
