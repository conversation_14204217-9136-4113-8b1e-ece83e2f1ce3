#!/usr/bin/python3
import requests
import json
import sys

# Function to retrieve the token
def get_token():
    token_response = requests.post(
        'https://127.0.0.1:/am/oauth/token',
        json={
            "client_secret": "1d737b2a-b4db-11ec-b909-0242ac120002",
            "grant_type": "client_credentials",
            "client_id": "32e49124-b4db-11ec-b909-0242ac120002"
        },
        verify=False  # Disable SSL verification (use cautiously in production)
    )

    if token_response.status_code != 200:
        print("Failed to retrieve access token.")
        sys.exit(1)

    token = token_response.json().get("access_token")
    return token

# Function to fetch and parse the health status
def check_services_up():
    token = get_token()

    # Fetch the health data
    health_response = requests.get(
        'https://127.0.0.1/am/actuator/health',
        headers={'Authorization': f'Bearer {token}'},
        verify=False  # Disable SSL verification
    )

    if health_response.status_code != 200:
        print("Failed to retrieve health status.")
        sys.exit(1)

    health_data = health_response.json()

    # Initialize an empty list to collect services that are down
    services_down = []

    # Check microservices status inside 'microServices'
    components = health_data.get('components', {})
    microservices = components.get('microServices', {}).get('details', {})

    # Loop through microservices and check their statuses
    for service_name, service_info in microservices.items():
        if 'status' in service_info and service_info['status'].get('status') != 'UP':
            services_down.append(service_name)
        # Some services may have components like 'diskSpace', 'ping', or 'rabbit', check them too
        for sub_component_name, sub_component_info in service_info.items():
            if isinstance(sub_component_info, dict) and sub_component_info.get('status') != 'UP':
                services_down.append(f"{service_name} - {sub_component_name}")

    return services_down

# Main execution
if __name__ == "__main__":
    services_down = check_services_up()

    if not services_down:
        print("All services are UP.")
        sys.exit(0)
    else:
        print("The following services are DOWN:")
        for service in services_down:
            print(f"- {service}")
        sys.exit(2)

