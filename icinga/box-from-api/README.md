# Scripts for BOX-IO with the use of the server API
These scripts are performed by taking the information from the server API. For this reason, it is necessary, before each check, to get a user token with the credentials of a certified user; this is done by calling the REST call **“Get user token"** as shown on Postman.

### check_websocketStatus.sh
_Goal_: check websocket status on each configuration.

_Use_: ./check_websocketStatus.sh

This check gets all the configurations with the REST call **“Get my installations”**, as shown on Postman, and checks the websocket's status for each of them. This information can be taken from the field _“configuration_service_status”_ of the output of the previously mentioned REST call, that can be _“reachable”_ or _“unreachable”_. If there are some endpoints whose websocket is _“unreachable”_, their MAC addresses are printed and an error is issued. Otherwise no error is given.

### check_mqttStatus.sh
_Goal_: check MQTT status on each configuration.

_Use_: ./check_mqttStatus.sh

This check gets all the configurations with the REST call **“Get my installations”**, as shown on Postman, and collects the ID and the corresponding MAC address on each of them. With this information, it calls the REST call **“Get mqtt status”**, as shown on Postman. The MQTT status can be taken from the field _“connected”_ of the output, that can be _“true”_ or _“false”_. If there are some endpoints whose MQTT is not connected, their MAC addresses are printed and an error is issued. Otherwise no error is given.

### check_connectionStatus.sh
_Goal_: check connection status of each endpoint in each configuration.

_Use_: ./check_connectionStatus.sh

This check gets all the configurations with the REST call **“Get my installations”**, as shown on Postman, and collects all the IDs of the configurations given. With this information, it calls the REST call **“Retrieve connection status”**, as shown on Postman. From its output, the check prints the connection status of each endpoint, grouping the endpoints by ID, area and partition, in this order.
