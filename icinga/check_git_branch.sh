#!/bin/bash 

#
# Check the branch of the docker-compose repository by giving the folder position
#
# ./check_git_branch.sh -l /opt/boxio -e dev-edalab



print_help() {
	echo "Check if the repository points to the correct branch."
	echo "Usage:"
	echo "[-l] repo directory"
	echo "[-e] expected branch"
	exit 0
}


#take arguments in input

while test -n "$1"; do
	case "$1" in
		--help|-h)
			print_help
			exit 0
			;;
		-l)
			dir=$2
			shift 
			;;
		-e)
			branch=$2
			shift
			;;
		*)
			echo "Unknown option: $1"
			print_help
			exit 3 
			;;
	esac 
	shift
done

#check if arguments are empty
if [ "$dir" == "" ]; then
	echo "No directory specified"
	print_help
	exit 3;
fi

if [ "$branch" == "" ]; then
	echo "No expected branch specified"
	print_help
	exit 3;
fi

strings=$(git -C /opt/boxio rev-parse --abbrev-ref HEAD)

#check if string is present
if [[ "$strings" == "$branch" ]]; then
	echo "Ok. branch is $branch"
	exit 0;
else
	echo "CRITICAL. branch is s$trings"
	exit 2;
fi
