#!/usr/bin/python3

import argparse
import requests
import sys
import json
import gitlab_helper

# Configuration
REPO_BASE = "registry.edalab.it/edalab"
gitlab_api_url = "https://git.edalab.it/api/v4"


def get_latest_tag_for_branch(project_id, branch_name, gitlab_token):
    """Get the latest tagged commit on the specified branch using GitLab API."""
    try:

        tags = gitlab_helper.get_all_tags(gitlab_token=gitlab_token, project_id=project_id)
        branch_commits = gitlab_helper.get_all_commits(
            project_id=project_id, branch_name=branch_name, gitlab_token=gitlab_token
        )
        # Find the latest commit on the branch that has an associated tag
        for commit in branch_commits:
            for tag in tags:
                if commit["id"] == tag["commit"]["id"]:
                    tag_name = tag["name"]
                    return tag_name

        return None  # No tagged commit found on the branch

    except requests.RequestException as e:
        print(f"UNKNOWN - Error retrieving tags from GitLab: {e}")
        sys.exit(3)  # Unknown status


def compare_file(gitlab_token, file):
    """Get the latest version for file both on repo and on machine"""
    try:
        version = "0"
        releases = gitlab_helper.get_previous_release_description(file["project_id"], gitlab_token)
        latest_release_name = releases[0]["name"]
        for path in file["paths"]:
            with open(path, "r", encoding="utf-8") as f:
                data = json.load(f)
                version = data["version"]
                if latest_release_name not in version:
                    print(
                        "VERSION MISMATCH! %s %s should be %s but is %s"
                        % (file["name"], path, latest_release_name, version)
                    )
                else:
                    print(f"OK is already running the latest version ({version}).")
        # return latest_release_name, version
    except requests.RequestException as e:
        print(f"UNKNOWN - Error retrieving tags from GitLab: {e}")
        sys.exit(3)  # Unknown status


def extract_project_path(image_name):
    """Extract the project path from a Docker image name."""
    try:
        path_parts = image_name.split("/")
        project_path = "/".join(path_parts[:-1])  # Join parts to form the project path
        return project_path
    except IndexError:
        print(f"UNKNOWN - Unable to extract project path from image: {image_name}")
        sys.exit(3)  # Unknown status


def compare_versions(gitlab_token):
    # Determine the branch to use based on the machine type
    error_list = []
    with open("release/config_file.json", "r", encoding="utf-8") as file:
        data = json.load(file)
    for service in data["services"]:
        project_id = service["id"]
        service_name = service["name"]
        branch = service["branch"]
        running_version = service["version"]
        if branch is None:
            continue
        latest_tag = get_latest_tag_for_branch(project_id, branch, gitlab_token)
        status = 0
        if not latest_tag:
            status = 3
            error_list.append(f"UNKNOWN - No tags found for branch: {branch} in {service_name}")
            if status < 3:
                status = 3
            continue
        # Compare versions
        if running_version == latest_tag or running_version in latest_tag:
            error_list.append(f"OK - {service_name} is already running the latest version ({running_version}).")
        else:
            error_list.append(
                f"CRITICAL - {service_name} updating {running_version}, to the latest version {latest_tag}."
            )
            service["version"] = latest_tag
            if status < 2:
                status = 2
    for error in error_list:
        print(error)
    for file in data["external_files"]:
        compare_file(gitlab_token, file)
    with open("release/config_file.json", "w", encoding="utf-8") as file:
        json.dump(data, file, indent=4)


def main():
    # Argument parsing
    parser = argparse.ArgumentParser(description="Check Docker service versions.")
    parser.add_argument("--gitlab-token", required=True, help="GitLab API token for authentication")
    args = parser.parse_args()
    compare_versions(gitlab_token=args.gitlab_token)


if __name__ == "__main__":
    main()
