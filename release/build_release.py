#!/usr/bin/env python3
# coding=UTF-8
import json
import sys
import urllib3
import slack_communication
import gitlab_helper

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
GITLAB_BASE_URL = "https://git.edalab.it"
GITLAB_API_URL = f"{GITLAB_BASE_URL}/api/v4"
GITLAB_RELEASES_URL = "/-/releases/"

DOCKER_COMPOSE_BOXIO_PROJECT_ID = 1034
dev_machines = ["box-io.edalab.it", "labiennale.edalab.it", "etensil.cloud"]


def retrieve_version_description(
    project_id, current_version, repo_url, project_name, gitlab_token, previous_version_response
):
    """
    Retrieve the description for the requested version and the version
    description
    """
    text = gitlab_helper.get_version_description(project_id, current_version, gitlab_token)
    version_url = f"{GITLAB_BASE_URL}{repo_url}{GITLAB_RELEASES_URL}{current_version}"
    version_title_string = retrieve_version_title_string(
        current_version, version_url, previous_version_response, project_name
    )
    version_description = gitlab_helper.get_version_description(project_id, current_version, gitlab_token)
    text = f"\n # {version_title_string} \n {version_description}"
    return text.rstrip("\r\n")


def retrieve_version_title_string(current_version, version_url, previous_version_response, project_name):
    contained = "rc" not in current_version and version_url in json.dumps(previous_version_response)
    if contained:
        return f"[{project_name} {current_version}]({version_url})"
    else:
        return f"[{project_name} {current_version} (NEW)]({version_url})"


def build_release(gitlab_token, slack_channel, slack_token):
    previous_version_response = gitlab_helper.get_previous_release_description(
        DOCKER_COMPOSE_BOXIO_PROJECT_ID, gitlab_token
    )
    release_description = ""
    with open("release/config_file.json", "r", encoding="utf-8") as file:
        data = json.load(file)
    for service in data["services"]:
        release_description += retrieve_version_description(
            service["id"],
            service["version"],
            service["repourl"],
            service["name"],
            gitlab_token,
            previous_version_response,
        )
    tag = data["TAG_NAME"]
    name = data["RELEASE_VERSION"]
    gitlab_helper.create_release(release_description, tag, name, DOCKER_COMPOSE_BOXIO_PROJECT_ID, gitlab_token)
    slack_communication.send_slack_message(
        channel=slack_channel,
        token=slack_token,
        message=f"La versione: https://git.edalab.it/edalab/boxio/cloud/docker/docker-compose-boxio/-/releases/{data['TAG_NAME']} è pronta per essere rilasciata sulle macchine {dev_machines}",
    )


def main(argv):
    """
    Main
    """
    build_release(argv[0], argv[1], argv[2])


if __name__ == "__main__":
    main(sys.argv[1:])
