#!/usr/bin/env python3
# coding=UTF-8
import json
import sys
import re

import requests
import gitlab_helper


gitlab_api_url = "https://git.edalab.it/api/v4"


def get_latest_tags_for_branch(project_id, branch_name, gitlab_token):
    """Get the latest tagged commit on the specified branch using GitLab API."""
    try:
        tags = gitlab_helper.get_all_tags(project_id, gitlab_token)
        branch_commits = gitlab_helper.get_all_commits(project_id, branch_name, gitlab_token)
        # Find the latest commit on the branch that has an associated tag and another one
        find = 0
        for commit in branch_commits:
            for tag in tags:
                if commit["id"] == tag["commit"]["id"]:
                    if find == 1:
                        return tag["name"]
                    find = find + 1

        return None  # No tagged commit found on the branch

    except requests.RequestException as e:
        print(f"UNKNOWN - Error retrieving tags from GitLab: {e}")
        sys.exit(3)  # Unknown status


def get_release_description(comparison):
    """
    Retrieve description for release
    """
    regex_issue = "(https:\/\/git.edalab.it.*?\/issues\/\d+)"
    regex_title = "(^[^\n]*)"
    features = []
    for x in comparison["commits"]:
        issue_description = x["message"]
        match_issue = exec(regex_issue, issue_description)

        if not match_issue:
            continue
        match_title = exec(regex_title, issue_description)

        title = match_title.pop()
        issue_url = match_issue.pop()
        features.append(f"{title} {issue_url}")
    release_description = "\n* ".join(features)
    release_description = f"* {release_description}"
    return release_description


def exec(regex, s):
    """
    Transposition of javascript exec function
    """
    m = re.search(regex, s)
    if m:
        return [s for s in m.groups()]


def release_creation(gitlab_token):
    with open("release/config_file.json", "r", encoding="utf-8") as file:
        data = json.load(file)
    for service in data["services"]:
        project_id = service["id"]
        tag = service["version"]
        branch = service["branch"]
        name = tag.split("-rc")[0]
        previous_tag = get_latest_tags_for_branch(project_id, branch, gitlab_token)
        if previous_tag is None:
            print("This service has no previous version", service)
            continue
        comparison = gitlab_helper.compare_tags_messages(tag, previous_tag, project_id, gitlab_token)
        release_description = get_release_description(comparison)
        try:
            gitlab_helper.create_release(release_description, tag, name, project_id, gitlab_token)
        except SystemExit as e:
            print(e)
            print("Will continue")


def main(argv):
    """
    Create all missing releases
    """
    release_creation(argv[0])


if __name__ == "__main__":
    main(sys.argv[1:])
