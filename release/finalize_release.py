#!/usr/bin/env python3
# coding=UTF-8
import configparser
import io
import json
import sys
from itertools import chain
from pathlib import Path

DIRECTORY_FOLDER = Path(__file__).resolve().parent.parent.absolute()
RELEASE_FOLDER = Path(__file__).parent.absolute()
CONFIG_FILE = BOXIO_MANAGER_DIR = Path.joinpath(
    DIRECTORY_FOLDER, RELEASE_FOLDER, 'config_file.json')
ENV_FILE = BOXIO_MANAGER_DIR = Path.joinpath(
    DIRECTORY_FOLDER, '.env')
BOXIO_MANAGER_FOLDER_NAME = 'boxio-manager'
CONF_MANAGER_FOLDER_NAME = 'configuration-manager'
STORM_FOLDER_NAME = 'storm'
TIMESCALE_FOLDER_NAME = 'timescale-data-access'
TIMESCALE_DATABASE_FOLDER_NAME = 'timescaledb'
BOXIO_MANAGER_CONF = Path.joinpath(
    DIRECTORY_FOLDER, B<PERSON>IO_MANAGER_FOLDER_NAME, 'root', 'config', 'application.properties_example')
CONF_MANAGER_CONF = Path.joinpath(
    DIRECTORY_FOLDER, CONF_MANAGER_FOLDER_NAME, 'root', 'config', 'application.properties_example')
TIMESCALE_CONF = Path.joinpath(
    DIRECTORY_FOLDER, TIMESCALE_FOLDER_NAME, 'root', 'config', 'application.properties_example')
STORM_CONF = Path.joinpath(
    DIRECTORY_FOLDER, STORM_FOLDER_NAME, 'root', 'config', 'config.properties_example')
BOXIO_MANAGER_CONF_COMPARE = Path.joinpath(
    DIRECTORY_FOLDER, BOXIO_MANAGER_FOLDER_NAME, 'root', 'config', 'application.properties')
CONF_MANAGER_CONF_COMPARE = Path.joinpath(
    DIRECTORY_FOLDER, CONF_MANAGER_FOLDER_NAME, 'root', 'config', 'application.properties')
TIMESCALE_CONF_COMPARE = Path.joinpath(
    DIRECTORY_FOLDER, TIMESCALE_FOLDER_NAME, 'root', 'config', 'application.properties')
STORM_CONF_COMPARE = Path.joinpath(
    DIRECTORY_FOLDER, STORM_FOLDER_NAME, 'root', 'config', 'config.properties')
CONFIGURATIONS_COMPARE = [BOXIO_MANAGER_CONF_COMPARE, CONF_MANAGER_CONF_COMPARE,
                          TIMESCALE_CONF_COMPARE, STORM_CONF_COMPARE
                          ]
CONFIGURATIONS = [BOXIO_MANAGER_CONF, CONF_MANAGER_CONF,
                  TIMESCALE_CONF, STORM_CONF]


def get_file_properties(file):
    '''
    Retrieve the properties in a file and the file lines
    '''
    properties = {}
    lines = []
    with open(file, encoding='utf-8') as conf_file:
        for line in conf_file:
            lines.append(line)
            if line.startswith('#') or line.startswith(' ') or '=' not in line:
                continue
            print(line)
            key, val = line.strip().split('=', 1)
            properties[key] = val
    return properties, lines


def manage_application_properties(config):
    '''
    Update the application_properties file

    Compare the actual properties file and the new one. For each value in new,
    add the property to actual. If the new property is a variable, it will be
    taken from the .env file
    '''
    properties_files = {'boxio-manager':
                        {'example_file': BOXIO_MANAGER_CONF,
                         'actual_file': BOXIO_MANAGER_CONF_COMPARE
                         },
                        'conf-manager': {
                            'example_file': CONF_MANAGER_CONF,
                            'actual_file': CONF_MANAGER_CONF_COMPARE
                        },
                        'timescale-data-access': {
                            'example_file': TIMESCALE_CONF,
                            'actual_file': TIMESCALE_CONF_COMPARE},
                        'storm': {
                            'example_file': STORM_CONF,
                            'actual_file': STORM_CONF_COMPARE
                        }}
    for keytext, valuetext in properties_files.items():
        properties, lines = get_file_properties(valuetext['actual_file'])
        properties_compared, lines_second = get_file_properties(
            valuetext['example_file'])
        new_keys = properties_compared.keys() - properties.keys()
        for key in new_keys:
            value = properties_compared[key]
            if '%' in value:
                new_txt = value.rstrip().split('%')
                new_txt[1] = config['default'][new_txt[1]]
                value = new_txt[1]+' \n'
            else:
                value = value+'\n'
            lines.append(f'{key}= {value}')

        with valuetext['actual_file'].open('w', encoding='utf-8') as file:
            file.writelines(lines)


def manage_env_file(env_file, config_file):
    '''
    Update the component version according to config_file.json file
    '''
    buf = io.StringIO()
    config = configparser.RawConfigParser(
        comment_prefixes='/', allow_no_value=True)
    config.optionxform = str
    with open(env_file, encoding='utf-8') as lines:
        lines = chain(('[default]',), lines)  # This line does the trick.
        config.read_file(lines)
    with config_file.open('r', encoding='utf-8') as json_file:
        variables = json.load(json_file)
        config.set('default', 'BOXIO_MANAGER_VERSION', variables['AM_VERSION'])
        config.set('default', 'CONFIGURATION_MANAGER_VERSION',
                   variables['CM_VERSION'])
        config.set('default', 'TIMESCALE_DATA_ACCESS_VERSION',
                   variables['T_VERSION'])
        config.set('default', 'STORM_VERSION', variables['S_VERSION'])
        config.set('default', 'T_DB_VERSION', variables['T_VERSION'])
        config.set('default', 'A_DB_VERSION', variables['A_DB_VERSION'])
        config.set('default', 'FRONTEND_VERSION', variables['FRONTEND_VERSION'])
    config.write(buf, space_around_delimiters=False)
    buf.seek(0)
    next(buf)
    with open(env_file, 'w', encoding='utf-8') as configfile:
        configfile.write(buf.read())
    return config


def main(argv):
    '''
    Update the env file and application.properties
    '''
    config = manage_env_file(ENV_FILE, CONFIG_FILE)
    manage_application_properties(config)


if __name__ == '__main__':
    main(sys.argv[1:])
