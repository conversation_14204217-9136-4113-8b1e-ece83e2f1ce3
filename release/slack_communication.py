import sys
import requests
import json


# Function to send a Slack message using Slack API
def send_slack_message(message, channel, token):
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}
    payload = {"channel": channel, "text": message}
    response = requests.post(
        "https://slack.com/api/chat.postMessage",
        headers=headers,
        data=json.dumps(payload),
    )
    if response.status_code != 200 or response.json().get("ok") is not True:
        raise ValueError(
            f"Slack API returned an error: {response.status_code}, Response: {response.text}"
        )


def main(argv):
    message = argv[0]
    slack_token = ""
    slack_channel = ""

    send_slack_message(message, slack_channel, slack_token)

    print("Comparison complete.")


if __name__ == "__main__":
    main(sys.argv[1:])
