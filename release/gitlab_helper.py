#!/usr/bin/env python3
# coding=UTF-8
import sys
import requests
import json

GITLAB_BASE_URL = "https://git.edalab.it"
GITLAB_API_URL = f"{GITLAB_BASE_URL}/api/v4"
GITLAB_RELEASES_URL = "/-/releases/"


def gitlab_api_get_request(endpoint, gitlab_token, params=None):
    headers = {
        "PRIVATE-TOKEN": gitlab_token,
        "Accept": "application/json",
        "Content-Type": "application/json",
    }
    try:
        response = requests.get(f"{GITLAB_API_URL}/{endpoint}", headers=headers, params=params)
        response.raise_for_status()  # Raises HTTPError for bad responses
        return json.loads(response.text.encode("utf8"))
    except requests.RequestException as e:
        print(f"Error with GitLab API request: {e}")
        sys.exit(3)


def gitlab_api_post_request(endpoint, body, gitlab_token):
    headers = {
        "PRIVATE-TOKEN": gitlab_token,
        "Accept": "application/json",
        "Content-Type": "application/json",
    }
    try:
        response = requests.post(
            f"{GITLAB_API_URL}/{endpoint}", data=json.dumps(body, indent=None).rstrip("\n"), headers=headers
        )
        response.raise_for_status()  # Raises HTTPError for bad responses
        return json.loads(response.text.encode("utf8"))
    except requests.RequestException as e:
        print(f"Error with GitLab API request: {e}")
        print(f"{endpoint} {body}")

        sys.exit(3)


def get_all_tags(project_id, gitlab_token):
    return gitlab_api_get_request(f"projects/{project_id}/repository/tags?page=1&per_page=100", gitlab_token)


def get_all_commits(project_id, branch_name, gitlab_token):
    return gitlab_api_get_request(
        f"projects/{project_id}/repository/commits?ref_name={branch_name}&page=1&per_page=100", gitlab_token
    )


def get_previous_release_description(project_id, gitlab_token):
    return gitlab_api_get_request(f"projects/{project_id}/releases?page=1&per_page=100", gitlab_token)


def get_version_description(project_id, current_version, gitlab_token):
    try:
        data = gitlab_api_get_request(f"projects/{project_id}/releases/{current_version}", gitlab_token)
        return data["description"]
    except SystemExit:
        return "MISSING DESCRIPTION"


def compare_tags_messages(now, previous, project_id, gitlab_token):
    params = {"from": previous, "to": now}
    return gitlab_api_get_request(f"projects/{project_id}/repository/compare", gitlab_token, params)


def create_release(description, tag, name, project_id, gitlab_token):
    body = {
        "tag_name": tag,
        "name": name,
        "description": description,
        "id": project_id,
    }
    return gitlab_api_post_request(f"projects/{project_id}/releases", body=body, gitlab_token=gitlab_token)
