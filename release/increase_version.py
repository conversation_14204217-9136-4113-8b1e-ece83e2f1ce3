#!/usr/bin/env python3
# coding=UTF-8
import json
from pathlib import Path
import subprocess

DIRECTORY_FOLDER = Path(__file__).resolve().parent.parent.absolute()
RELEASE_FOLDER = Path(__file__).parent.absolute()
CONFIG_FILE = BOXIO_MANAGER_DIR = Path.joinpath(DIRECTORY_FOLDER, RELEASE_FOLDER, "config_file.json")


def increase_version(version):
    """
    Increase version by one
    """
    if "-" in version:
        last_part = version.split("-rc")
        return ("-rc").join(increase_number(last_part))
    else:
        last_part = version.split(".")
        return (".").join(increase_number(last_part))


def increase_number(last_part):
    """
    Increase version number, already splitted, by one
    """
    last = last_part[-1]
    increased = int(last) + 1
    last_part[-1] = str(increased)
    return last_part


def run_command(command):
    """Utility to run shell commands"""
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error running command: {command}")
        print(result.stderr)
        return None
    return result.stdout.strip()


def main():
    """
    Increase the version of the full release
    """
    with CONFIG_FILE.open("r", encoding="utf-8") as json_file:
        variables = json.load(json_file)
    result = increase_version(variables["TAG_NAME"])
    variables["TAG_NAME"] = result
    with CONFIG_FILE.open("w", encoding="utf-8") as json_file:
        json.dump(variables, json_file, indent=4)
    print(variables["TAG_NAME"])


if __name__ == "__main__":
    main()
