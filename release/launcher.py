import sys
import compare_versions
import release_creation
import increase_version
import build_release
import json
from pathlib import Path


DIRECTORY_FOLDER = Path(__file__).resolve().parent.parent.absolute()
RELEASE_FOLDER = Path(__file__).parent.absolute()
CONFIG_FILE = BOXIO_MANAGER_DIR = Path.joinpath(DIRECTORY_FOLDER, RELEASE_FOLDER, "config_file.json")


def stage_version(new_version):
    increase_version.run_command("git add .")
    commit_message = "Bump version"
    increase_version.run_command(f'git commit -m "{commit_message}"')
    increase_version.run_command(f"git tag {new_version}")
    increase_version.run_command("git push --tags")
    increase_version.run_command("git push")
    print(f"Version bumped and tagged as {new_version}")


def retrieve_version_to_tag():
    with CONFIG_FILE.open("r", encoding="utf-8") as json_file:
        variables = json.load(json_file)
    return variables["TAG_NAME"]


def main(gitlab_token, slack_channel, slack_token):
    compare_versions.compare_versions(gitlab_token)
    release_creation.release_creation(gitlab_token)
    increase_version.main()
    version = retrieve_version_to_tag()
    stage_version(version)
    build_release.build_release(gitlab_token, slack_channel, slack_token)


if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python3 release_all.py <gitlab-token> <slack-channel> <slack-token>")
        sys.exit(1)

    main(sys.argv[1], sys.argv[2], sys.argv[3])
