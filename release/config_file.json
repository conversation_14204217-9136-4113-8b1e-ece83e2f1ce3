{"services": [{"name": "AUTHDB", "id": "1037", "version": "0.6.6", "branch": "main", "repourl": "edalab/boxio/cloud/docker/postgres", "container": "postgres"}, {"name": "COUCHDB", "id": "1038", "version": "0.2.0", "branch": "main", "repourl": "edalab/boxio/cloud/docker/couchdb", "container": "couchdb"}, {"name": "CONFIGURATION_MANAGER", "id": "884", "branch": "main", "version": "0.2.19", "repourl": "edalab/boxio/cloud/configuration-manager", "container": "configuration-manager"}, {"name": "BOXIO_MANAGER", "id": "882", "branch": "main", "version": "0.2.54", "repourl": "edalab/boxio/cloud/auth-manager", "container": "auth-manager"}, {"name": "STORM", "id": "885", "branch": "main", "version": "0.2.20", "repourl": "edalab/boxio/cloud/storm", "container": "storm"}, {"name": "TIMESCALE_DATA_ACCESS", "id": "883", "branch": "main", "version": "0.2.8", "repourl": "edalab/boxio/cloud/timescale-data-access", "container": "timescale-data-access"}, {"name": "EVENT_MANAGER", "branch": "main", "id": "878", "version": "0.5.9", "repourl": "edalab/boxio/cloud/event-service", "container": "event-service"}, {"name": "HEALTH_SERVICE", "id": "876", "branch": "main", "version": "1.1.10", "repourl": "edalab/boxio/cloud/health-service", "container": "health-service"}, {"name": "DISK_SIZE", "id": "874", "branch": "main", "version": "0.1.5", "repourl": "edalab/boxio/cloud/disk-size", "container": "disk-size"}, {"name": "ASSISTANT_SERVICE", "id": "1221", "branch": "main", "version": "0.2.3", "repourl": "edalab/boxio/cloud/assistant-service", "container": "assistant-service"}, {"name": "CALL_SERVICE", "id": "1218", "branch": "main", "version": "0.1.3", "repourl": "edalab/boxio/cloud/call-service", "container": "call-service"}, {"name": "WE_FRONTEND", "id": "1187", "branch": "main", "version": "web-editor/3.0.0-rc11", "repourl": "edalab/boxio/gui/monorepo-gui", "container": "monorepo-gui"}, {"name": "MQTT", "id": "1036", "branch": "main", "version": "0.0.4", "repourl": "edalab/boxio/cloud/docker/emqttd", "container": "emqttd"}], "external_files": [{"name": "boxio-endpoints", "paths": ["shared/config/config.json", "shared/config/fiam-config.json"], "executive_path": "shared/config/endpoint.json", "repourl": "edalab/boxio/configuration/boxio-endpoints", "project_id": "887"}], "RELEASE_VERSION": "2.0.1", "TAG_NAME": "2.0.1-rc43"}