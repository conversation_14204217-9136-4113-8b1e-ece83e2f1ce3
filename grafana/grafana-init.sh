#! /bin/bash

URL="http://cloud.edalab.it:3000"
AUTH="admin:admin"
JSON_DIRECTORY="./resources"
ORG_NAME=edalab
USR_NAME=edalab
USR_EMAIL=<EMAIL>
USR_PASS=edalab2021

main() 
{  
  echo "=== CREATING ORGANIZATION ==="
  echo "name " $ORG_NAME
  org_id=$(create_org "$ORG_NAME")
  echo "name " $ORG_NAME
  echo "orgId: " $org_id 
  echo
  
  echo "=== CHANGING ACTIVE ORGANIZATION ==="
  change_active_org $org_id
  echo

  echo "=== CREATING API TOKEN ==="
  res=$(get_api_token)
   echo $res
  key=$(echo $res | jq -r '.key')
  echo "key " $key
  echo
   sleep 1

  echo "=== ADDING NEW GLOBAL USER ==="
  res=$(new_user)
  echo $res
  user_id=$(echo $res | jq '.id')
  echo "User id " $user_id
  echo
   sleep 1

  echo "=== ADDING USER IN ORG ==="
  res=$(add_user_in_org $org_id)
  echo $res
  message=$(echo $res | jq -r '.message')
  echo $message
  echo
   sleep 1

  echo "=== ORGS LIST ==="
  res=$(get_orgs_for_user $user_id)
  echo $res
  echo
   sleep 1

  echo "=== REMOVING USER FROM MAIN ORG ==="
  res=$(remove_usr_org 1 $user_id)
   echo $res
  message=$(echo $res | jq -r '.message')
  echo $message
  echo
   sleep 1

  echo "=== ORGS LIST ==="
  res=$(get_orgs_for_user $user_id)
  echo $res
  echo
   sleep 1

  echo "=== ADDING DATASOURCE ==="
  res=$(add_datasource "$key" "$JSON_DIRECTORY/Datasource.json")
  echo $res
  message=$(echo $res | jq -r '.message')
  name=$(echo $res | jq -r '.name')
  echo $name  $message
  echo  
  sleep 1

  echo "=== ADDING DASHBOARD ==="

  dash=$(cat "$JSON_DIRECTORY/Temperature.json") 

  res=$(add_dashboard "$key" "$dash")
  echo $res
  dashboard_id=$(echo $res | jq -r .id)
  dashboard_url=$(echo $res | jq -r .url)
  status=$(echo $res | jq -r .status)

  echo id: $dashboard_id, url: $dashboard_url, status: $status
  echo 
  
  echo "=== SETTING HOME DASHBOARD ==="
  res=$(star_dashboard "$key" $dashboard_id)
  msg=$(echo $res | jq -r '.message')
  echo $msg
  echo 
  sleep 1
  echo
  
  echo "=== ADDING DASHBOARD ==="

  dash=$(cat "$JSON_DIRECTORY/Humidity.json") 

  res=$(add_dashboard "$key" "$dash")
  echo $res
  dashboard_id=$(echo $res | jq -r .id)
  dashboard_url=$(echo $res | jq -r .url)
  status=$(echo $res | jq -r .status)

  echo id: $dashboard_id, url: $dashboard_url, status: $status
  echo 

}

function create_org() {

  curl -X POST \
    --silent \
    --user "$AUTH" \
    $URL/api/orgs \
    -H 'Content-Type: application/json' \
    -d '{
      "name":"'$1'"
    }' | jq -r '.orgId'
}

function add_user_in_org() {
  curl -X POST \
    --silent \
    --user "$AUTH" \
    $URL/api/orgs/$1/users \
    -H 'Content-Type: application/json' \
    -d '{
      "loginOrEmail":"'$USR_NAME'",
      "role":"Viewer"
    }'
}

function get_orgs_for_user() {
  curl -X GET \
    --silent \
    --user "$AUTH" \
    $URL/api/users/$1/orgs \
    -H 'Content-Type: application/json'
}

function remove_usr_org() {
  curl -X DELETE \
    --silent \
    --user "$AUTH" \
    $URL/api/orgs/$1/users/$2 \
    -H 'Content-Type: application/json'
}

function change_active_org() {
  curl -X POST \
    --silent \
    --user "$AUTH" \
    $URL/api/user/using/$1 |
  jq -r '.message'
}

function get_api_token() {
  timestamp=$(date +"%s")
  curl -X POST \
    --silent \
    --user "$AUTH" \
    $URL/api/auth/keys \
    -H 'Content-Type: application/json' \
    -d '{
    "name":"'$ORG_NAME"_"$timestamp'",
    "role": "Admin"
  }' 
}

function new_user() {
  curl -X POST \
    --silent \
    --user "$AUTH" \
    $URL/api/admin/users \
    -H 'Content-Type: application/json' \
    -d '{
      "name":"'$USR_NAME'",
      "email":"'$USR_EMAIL'",
      "login":"'$USR_NAME'",
      "password":"'$USR_PASS'"
    }' 
}

function add_datasource() {
  curl -X POST \
    --silent \
    --insecure \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $1" \
    --data-binary @"$2" \
    $URL/api/datasources
}

function add_dashboard() {
  curl -X POST \
    --silent \
    $URL/api/dashboards/db \
    --insecure \
    -H "Authorization: Bearer $1" \
    -H "Content-Type: application/json" \
    -d "$2"
}

function add_dashboard_file() {
  curl -X POST \
    --silent \
    $URL/api/dashboards/db \
    --insecure \
    -H "Authorization: Bearer $1" \
    -H "Content-Type: application/json" \
    --data-binary @$2 |
  jq '.id'
}

function star_dashboard() {
  curl -X PUT \
    --silent \
    $URL/api/org/preferences \
    --insecure \
    -H "Authorization: Bearer $1" \
    -H "Content-Type: application/json" \
    -d '{
      "theme": "",
      "homeDashboardId":'$2',
      "timezone":"utc"
    }'
}

main
