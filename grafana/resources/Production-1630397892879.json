{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 4, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": false, "title": "Pages", "tooltip": "", "type": "dashboards", "url": ""}], "panels": [{"datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "flowchartsData": {"flowcharts": [{"allowDrawio": false, "bgColor": null, "center": true, "csv": "## See more information for the syntax at https://drawio-app.com/import-from-csv-to-drawio/\n##\n## Example CSV. Use ## for comments and # for configuration.\n## The following names are reserved and should not be used (or ignored):\n## id, tooltip, placeholder(s), link and label (see below)\n##\n#\n## Node label with placeholders and HTML.\n## Default is '%name_of_first_column%'.\n#\n# label: %name%<br><i style=\"color:gray;\">%position%</i><br><a href=\"mailto:%email%\">Email</a>\n#\n## Node style (placeholders are replaced once).\n## Default is the current style for nodes.\n#\n# style: label;image=%image%;whiteSpace=wrap;html=1;rounded=1;fillColor=%fill%;strokeColor=%stroke%;\n#\n## Parent style for nodes with child nodes (placeholders are replaced once).\n#\n# parentstyle: swimlane;whiteSpace=wrap;html=1;childLayout=stackLayout;horizontal=1;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;\n#\n## Optional column name that contains a reference to a named style in styles.\n## Default is the current style for nodes.\n#\n# stylename: -\n#\n## JSON for named styles of the form {\"name\": \"style\", \"name\": \"style\"} where style is a cell style with\n## placeholders that are replaced once.\n#\n# styles: -\n#\n## Optional column name that contains a reference to a named label in labels.\n## Default is the current label.\n#\n# labelname: -\n#\n## JSON for named labels of the form {\"name\": \"label\", \"name\": \"label\"} where label is a cell label with\n## placeholders.\n#\n# labels: -\n#\n## Uses the given column name as the identity for cells (updates existing cells).\n## Default is no identity (empty value or -).\n#\n# identity: -\n#\n## Uses the given column name as the parent reference for cells. Default is no parent (empty or -).\n## The identity above is used for resolving the reference so it must be specified.\n#\n# parent: -\n#\n## Adds a prefix to the identity of cells to make sure they do not collide with existing cells (whose\n## IDs are numbers from 0..n, sometimes with a GUID prefix in the context of realtime collaboration).\n## Default is csvimport-.\n#\n# namespace: csvimport-\n#\n## Connections between rows (\"from\": source colum, \"to\": target column).\n## Label, style and invert are optional. Defaults are '', current style and false.\n## If placeholders are used in the style, they are replaced with data from the source.\n## An optional placeholders can be set to target to use data from the target instead.\n## In addition to label, an optional fromlabel and tolabel can be used to name the column\n## that contains the text for the label in the edges source or target (invert ignored).\n## The label is concatenated in the form fromlabel + label + tolabel if all are defined.\n## The target column may contain a comma-separated list of values.\n## Multiple connect entries are allowed.\n#\n# connect: {\"from\": \"manager\", \"to\": \"name\", \"invert\": true, \"label\": \"manages\", \\\n#          \"style\": \"curved=1;endArrow=blockThin;endFill=1;fontSize=11;\"}\n# connect: {\"from\": \"refs\", \"to\": \"id\", \"style\": \"curved=1;fontSize=11;\"}\n#\n## Node x-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# left: \n#\n## Node y-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# top: \n#\n## Node width. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the width. Default is auto.\n#\n# width: auto\n#\n## Node height. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the height. Default is auto.\n#\n# height: auto\n#\n## Padding for autosize. Default is 0.\n#\n# padding: -12\n#\n## Comma-separated list of ignored columns for metadata. (These can be\n## used for connections and styles but will not be added as metadata.)\n#\n# ignore: id,image,fill,stroke,refs,manager\n#\n## Column to be renamed to link attribute (used as link).\n#\n# link: url\n#\n## Spacing between nodes. Default is 40.\n#\n# nodespacing: 40\n#\n## Spacing between levels of hierarchical layouts. Default is 100.\n#\n# levelspacing: 100\n#\n## Spacing between parallel edges. Default is 40. Use 0 to disable.\n#\n# edgespacing: 40\n#\n## Name or JSON of layout. Possible values are auto, none, verticaltree, horizontaltree,\n## verticalflow, horizontalflow, organic, circle or a JSON string as used in Layout, Apply.\n## Default is auto.\n#\n# layout: auto\n#\n## ---- CSV below this line. First line are column names. ----\nname,position,id,location,manager,email,fill,stroke,refs,url,image\nEvan Miller,CFO,emi,Office 1,,<EMAIL>,#dae8fc,#6c8ebf,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-9-2-128.png\nEdward Morrison,Brand Manager,emo,Office 2,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-10-3-128.png\nRon Donovan,System Admin,rdo,Office 3,Evan Miller,<EMAIL>,#d5e8d4,#82b366,\"emo,tva\",https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-2-128.png\nTessa Valet,HR Director,tva,Office 4,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-3-128.png\n", "download": false, "editorTheme": "dark", "editorUrl": "https://www.draw.io", "enableAnim": true, "grid": false, "lock": true, "name": "Main", "scale": true, "tooltip": true, "type": "xml", "url": "http://<YourUrl>/<Your XML/drawio file/api>", "xml": "<mxfile host=\"app.diagrams.net\" modified=\"2021-08-27T13:28:50.555Z\" agent=\"5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36\" etag=\"RE26-HteRQaUXmAZ33HL\" version=\"14.9.9\" type=\"embed\"><diagram id=\"42Zp2s0p4LsoEojTpR9-\" name=\"Page-1\">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</diagram></mxfile>", "zoom": "100%"}]}, "format": "short", "graphId": "flowchart_5", "gridPos": {"h": 5, "w": 6, "x": 0, "y": 0}, "id": 5, "newFlag": false, "pluginVersion": "7.5.7", "rulesData": {"rulesData": [{"aggregation": "current", "alias": "air quality", "colors": ["rgba(50, 172, 45, 0.97)", "#F2CC0C", "#f16330e4", "rgba(245, 54, 54, 0.9)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 1, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "A", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "34", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/", "/.*/"], "textData": [{"hidden": false, "pattern": "", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [1000, 3000, 10000], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "short", "valueData": []}]}, "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "select avg(value::real) as value from allparameterstates_last where (endpoint = 'AQS-3' or endpoint = 'AQS-4') and parameter = 'voc';", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Air Quality", "type": "agenty-flowcharting-panel", "valueName": "current", "version": "0.9.0"}, {"datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "flowchartsData": {"flowcharts": [{"allowDrawio": false, "bgColor": null, "center": true, "csv": "## See more information for the syntax at https://drawio-app.com/import-from-csv-to-drawio/\n##\n## Example CSV. Use ## for comments and # for configuration.\n## The following names are reserved and should not be used (or ignored):\n## id, tooltip, placeholder(s), link and label (see below)\n##\n#\n## Node label with placeholders and HTML.\n## Default is '%name_of_first_column%'.\n#\n# label: %name%<br><i style=\"color:gray;\">%position%</i><br><a href=\"mailto:%email%\">Email</a>\n#\n## Node style (placeholders are replaced once).\n## Default is the current style for nodes.\n#\n# style: label;image=%image%;whiteSpace=wrap;html=1;rounded=1;fillColor=%fill%;strokeColor=%stroke%;\n#\n## Parent style for nodes with child nodes (placeholders are replaced once).\n#\n# parentstyle: swimlane;whiteSpace=wrap;html=1;childLayout=stackLayout;horizontal=1;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;\n#\n## Optional column name that contains a reference to a named style in styles.\n## Default is the current style for nodes.\n#\n# stylename: -\n#\n## JSON for named styles of the form {\"name\": \"style\", \"name\": \"style\"} where style is a cell style with\n## placeholders that are replaced once.\n#\n# styles: -\n#\n## Optional column name that contains a reference to a named label in labels.\n## Default is the current label.\n#\n# labelname: -\n#\n## JSON for named labels of the form {\"name\": \"label\", \"name\": \"label\"} where label is a cell label with\n## placeholders.\n#\n# labels: -\n#\n## Uses the given column name as the identity for cells (updates existing cells).\n## Default is no identity (empty value or -).\n#\n# identity: -\n#\n## Uses the given column name as the parent reference for cells. Default is no parent (empty or -).\n## The identity above is used for resolving the reference so it must be specified.\n#\n# parent: -\n#\n## Adds a prefix to the identity of cells to make sure they do not collide with existing cells (whose\n## IDs are numbers from 0..n, sometimes with a GUID prefix in the context of realtime collaboration).\n## Default is csvimport-.\n#\n# namespace: csvimport-\n#\n## Connections between rows (\"from\": source colum, \"to\": target column).\n## Label, style and invert are optional. Defaults are '', current style and false.\n## If placeholders are used in the style, they are replaced with data from the source.\n## An optional placeholders can be set to target to use data from the target instead.\n## In addition to label, an optional fromlabel and tolabel can be used to name the column\n## that contains the text for the label in the edges source or target (invert ignored).\n## The label is concatenated in the form fromlabel + label + tolabel if all are defined.\n## The target column may contain a comma-separated list of values.\n## Multiple connect entries are allowed.\n#\n# connect: {\"from\": \"manager\", \"to\": \"name\", \"invert\": true, \"label\": \"manages\", \\\n#          \"style\": \"curved=1;endArrow=blockThin;endFill=1;fontSize=11;\"}\n# connect: {\"from\": \"refs\", \"to\": \"id\", \"style\": \"curved=1;fontSize=11;\"}\n#\n## Node x-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# left: \n#\n## Node y-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# top: \n#\n## Node width. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the width. Default is auto.\n#\n# width: auto\n#\n## Node height. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the height. Default is auto.\n#\n# height: auto\n#\n## Padding for autosize. Default is 0.\n#\n# padding: -12\n#\n## Comma-separated list of ignored columns for metadata. (These can be\n## used for connections and styles but will not be added as metadata.)\n#\n# ignore: id,image,fill,stroke,refs,manager\n#\n## Column to be renamed to link attribute (used as link).\n#\n# link: url\n#\n## Spacing between nodes. Default is 40.\n#\n# nodespacing: 40\n#\n## Spacing between levels of hierarchical layouts. Default is 100.\n#\n# levelspacing: 100\n#\n## Spacing between parallel edges. Default is 40. Use 0 to disable.\n#\n# edgespacing: 40\n#\n## Name or JSON of layout. Possible values are auto, none, verticaltree, horizontaltree,\n## verticalflow, horizontalflow, organic, circle or a JSON string as used in Layout, Apply.\n## Default is auto.\n#\n# layout: auto\n#\n## ---- CSV below this line. First line are column names. ----\nname,position,id,location,manager,email,fill,stroke,refs,url,image\nEvan Miller,CFO,emi,Office 1,,<EMAIL>,#dae8fc,#6c8ebf,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-9-2-128.png\nEdward Morrison,Brand Manager,emo,Office 2,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-10-3-128.png\nRon Donovan,System Admin,rdo,Office 3,Evan Miller,<EMAIL>,#d5e8d4,#82b366,\"emo,tva\",https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-2-128.png\nTessa Valet,HR Director,tva,Office 4,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-3-128.png\n", "download": false, "editorTheme": "dark", "editorUrl": "https://www.draw.io", "enableAnim": true, "grid": false, "lock": true, "name": "Main", "scale": true, "tooltip": true, "type": "xml", "url": "http://<YourUrl>/<Your XML/drawio file/api>", "xml": "<mxfile host=\"app.diagrams.net\" modified=\"2021-08-27T13:07:19.737Z\" agent=\"5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36\" version=\"14.9.9\" etag=\"zfSMvIEcDEg6rxeRT59t\" type=\"embed\"><diagram id=\"mjnYC13O4Iv-KsGaUhJ6\" name=\"Page-1\">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</diagram></mxfile>", "zoom": "100%"}]}, "format": "short", "graphId": "flowchart_6", "gridPos": {"h": 5, "w": 6, "x": 6, "y": 0}, "id": 6, "newFlag": false, "pluginVersion": "7.5.7", "rulesData": {"rulesData": [{"aggregation": "current", "alias": "occupancy", "colors": ["rgba(171, 171, 171, 0.97)", "#3274D9"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 1, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "A", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "32", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/"], "textData": [{"hidden": false, "pattern": "", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "short", "valueData": []}]}, "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where (endpoint = 'MOS-3' or endpoint = 'MOS-4') and ((parameter = 'occupancy' and value = 'true') or (parameter = 'movimento' and value = 'true'));", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Occupancy", "type": "agenty-flowcharting-panel", "valueName": "current", "version": "0.9.0"}, {"datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "flowchartsData": {"flowcharts": [{"allowDrawio": false, "bgColor": null, "center": true, "csv": "## See more information for the syntax at https://drawio-app.com/import-from-csv-to-drawio/\n##\n## Example CSV. Use ## for comments and # for configuration.\n## The following names are reserved and should not be used (or ignored):\n## id, tooltip, placeholder(s), link and label (see below)\n##\n#\n## Node label with placeholders and HTML.\n## Default is '%name_of_first_column%'.\n#\n# label: %name%<br><i style=\"color:gray;\">%position%</i><br><a href=\"mailto:%email%\">Email</a>\n#\n## Node style (placeholders are replaced once).\n## Default is the current style for nodes.\n#\n# style: label;image=%image%;whiteSpace=wrap;html=1;rounded=1;fillColor=%fill%;strokeColor=%stroke%;\n#\n## Parent style for nodes with child nodes (placeholders are replaced once).\n#\n# parentstyle: swimlane;whiteSpace=wrap;html=1;childLayout=stackLayout;horizontal=1;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;\n#\n## Optional column name that contains a reference to a named style in styles.\n## Default is the current style for nodes.\n#\n# stylename: -\n#\n## JSON for named styles of the form {\"name\": \"style\", \"name\": \"style\"} where style is a cell style with\n## placeholders that are replaced once.\n#\n# styles: -\n#\n## Optional column name that contains a reference to a named label in labels.\n## Default is the current label.\n#\n# labelname: -\n#\n## JSON for named labels of the form {\"name\": \"label\", \"name\": \"label\"} where label is a cell label with\n## placeholders.\n#\n# labels: -\n#\n## Uses the given column name as the identity for cells (updates existing cells).\n## Default is no identity (empty value or -).\n#\n# identity: -\n#\n## Uses the given column name as the parent reference for cells. Default is no parent (empty or -).\n## The identity above is used for resolving the reference so it must be specified.\n#\n# parent: -\n#\n## Adds a prefix to the identity of cells to make sure they do not collide with existing cells (whose\n## IDs are numbers from 0..n, sometimes with a GUID prefix in the context of realtime collaboration).\n## Default is csvimport-.\n#\n# namespace: csvimport-\n#\n## Connections between rows (\"from\": source colum, \"to\": target column).\n## Label, style and invert are optional. Defaults are '', current style and false.\n## If placeholders are used in the style, they are replaced with data from the source.\n## An optional placeholders can be set to target to use data from the target instead.\n## In addition to label, an optional fromlabel and tolabel can be used to name the column\n## that contains the text for the label in the edges source or target (invert ignored).\n## The label is concatenated in the form fromlabel + label + tolabel if all are defined.\n## The target column may contain a comma-separated list of values.\n## Multiple connect entries are allowed.\n#\n# connect: {\"from\": \"manager\", \"to\": \"name\", \"invert\": true, \"label\": \"manages\", \\\n#          \"style\": \"curved=1;endArrow=blockThin;endFill=1;fontSize=11;\"}\n# connect: {\"from\": \"refs\", \"to\": \"id\", \"style\": \"curved=1;fontSize=11;\"}\n#\n## Node x-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# left: \n#\n## Node y-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# top: \n#\n## Node width. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the width. Default is auto.\n#\n# width: auto\n#\n## Node height. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the height. Default is auto.\n#\n# height: auto\n#\n## Padding for autosize. Default is 0.\n#\n# padding: -12\n#\n## Comma-separated list of ignored columns for metadata. (These can be\n## used for connections and styles but will not be added as metadata.)\n#\n# ignore: id,image,fill,stroke,refs,manager\n#\n## Column to be renamed to link attribute (used as link).\n#\n# link: url\n#\n## Spacing between nodes. Default is 40.\n#\n# nodespacing: 40\n#\n## Spacing between levels of hierarchical layouts. Default is 100.\n#\n# levelspacing: 100\n#\n## Spacing between parallel edges. Default is 40. Use 0 to disable.\n#\n# edgespacing: 40\n#\n## Name or JSON of layout. Possible values are auto, none, verticaltree, horizontaltree,\n## verticalflow, horizontalflow, organic, circle or a JSON string as used in Layout, Apply.\n## Default is auto.\n#\n# layout: auto\n#\n## ---- CSV below this line. First line are column names. ----\nname,position,id,location,manager,email,fill,stroke,refs,url,image\nEvan Miller,CFO,emi,Office 1,,<EMAIL>,#dae8fc,#6c8ebf,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-9-2-128.png\nEdward Morrison,Brand Manager,emo,Office 2,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-10-3-128.png\nRon Donovan,System Admin,rdo,Office 3,Evan Miller,<EMAIL>,#d5e8d4,#82b366,\"emo,tva\",https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-2-128.png\nTessa Valet,HR Director,tva,Office 4,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-3-128.png\n", "download": false, "editorTheme": "dark", "editorUrl": "https://www.draw.io", "enableAnim": true, "grid": false, "lock": true, "name": "Main", "scale": true, "tooltip": true, "type": "xml", "url": "http://<YourUrl>/<Your XML/drawio file/api>", "xml": "<mxGraphModel><root><mxCell id=\"0\"/><mxCell id=\"1\" parent=\"0\"/><mxCell id=\"35\" value=\"\" style=\"ellipse;whiteSpace=wrap;html=1;fillColor=#1f60c4;\" parent=\"1\" vertex=\"1\"><mxGeometry x=\"285\" y=\"190\" width=\"150\" height=\"140\" as=\"geometry\"/></mxCell><mxCell id=\"33\" value=\"\" style=\"shape=image;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;\" parent=\"1\" vertex=\"1\"><mxGeometry x=\"314\" y=\"214\" width=\"92\" height=\"92\" as=\"geometry\"/></mxCell></root></mxGraphModel>", "zoom": "100%"}]}, "format": "short", "graphId": "flowchart_7", "gridPos": {"h": 5, "w": 6, "x": 12, "y": 0}, "id": 7, "newFlag": false, "pluginVersion": "7.5.7", "rulesData": {"rulesData": [{"aggregation": "current", "alias": "window", "colors": ["rgba(171, 171, 171, 0.97)", "#1F60C4"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 1, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "A", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "35", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/"], "textData": [{"hidden": false, "pattern": "", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "short", "valueData": []}]}, "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where endpoint = 'WIN-3' and parameter = 'opening' and value = 'true';", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Window opening", "type": "agenty-flowcharting-panel", "valueName": "current", "version": "0.9.0"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "rgb(255, 255, 255)", "value": null}]}, "unit": "celsius"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 0}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"valueSize": 125}, "textMode": "auto"}, "pluginVersion": "7.5.7", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'TMST-3' and parameter = 'setpoint';", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "Thermostat", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  endpoint,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and parameter = 'temperatura'\n  and partition = 'Production'\n  and endpoint LIKE 'AQS%'\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Temperature", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External temperature"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "celsius", "label": "Temperature", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  endpoint,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and parameter = 'umidita'\n  and partition = 'Production'\n  and endpoint LIKE 'AQS%'\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External temperature"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "celsius", "label": "Temperature", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "ppm"}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 13}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  parameter,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and partition = 'Production'\n  and endpoint = 'PM10-3'\n  and parameter LIKE 'PM%'\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PM10-3", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External temperature"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "ppm", "label": "Temperature", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Production", "uid": "PViTbVVnz", "version": 17}