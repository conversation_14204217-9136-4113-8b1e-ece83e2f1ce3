{"dashboard": {"__inputs": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.5.7"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [{"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": false, "title": "Pages", "tooltip": "", "type": "dashboards", "url": ""}], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  endpoint,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and parameter = 'umidita'\n  and (partition = 'Production' or partition = 'Roof Top')\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Production", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External humidity"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  endpoint,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and parameter = 'umidita'\n  and (partition = 'Administration' or partition = 'Roof Top')\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Administration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External humidity"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  endpoint,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and parameter = 'umidita'\n  and (partition = 'Laboratory' or partition = 'Roof Top')\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Laboratory", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External humidity"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  endpoint,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and parameter = 'umidita'\n  and (partition = 'Server Room' or partition = 'Roof Top')\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Server Room", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External humidity"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.7", "pointradius": 0.5, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"format": "time_series", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  $__time(time),\n  endpoint,\n  value::numeric\nFROM\n  measurements\nWHERE\n  $__timeFilter(time)\n  and installation = 'db_20200123082538_0268164331'\n  and parameter = 'umidita'\n  and (partition = 'Meeting Room' or partition = 'Roof Top')\nORDER BY time desc\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Meeting Room", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "Temp-Hum-PM", "renamePattern": "External humidity"}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:699", "format": "humidity", "label": "<PERSON><PERSON><PERSON><PERSON>", "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:700", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "<PERSON><PERSON><PERSON><PERSON>", "uid": "AsleRP6Mz", "version": 5}}