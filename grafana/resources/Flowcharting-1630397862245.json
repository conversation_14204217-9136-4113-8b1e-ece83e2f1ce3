{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 3, "links": [], "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 27}, {"color": "red", "value": 30}]}, "unit": "celsius"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 0, "y": 0}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 100, "valueSize": 125}, "textMode": "auto"}, "pluginVersion": "7.5.7", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'Temp-Hum-PM' and parameter = 'temperatura';\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "San Giovanni Lupatato external temperature", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 50}, {"color": "red", "value": 65}]}, "unit": "humidity"}, "overrides": []}, "gridPos": {"h": 5, "w": 12, "x": 12, "y": 0}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"titleSize": 100, "valueSize": 125}, "textMode": "auto"}, "pluginVersion": "7.5.7", "targets": [{"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'Temp-Hum-PM' and parameter = 'umidita';\n", "refId": "A", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "title": "San Giovanni Lupatoto external humidity", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "flowchartsData": {"flowcharts": [{"allowDrawio": false, "bgColor": "#fff", "center": true, "csv": "## See more information for the syntax at https://drawio-app.com/import-from-csv-to-drawio/\n##\n## Example CSV. Use ## for comments and # for configuration.\n## The following names are reserved and should not be used (or ignored):\n## id, tooltip, placeholder(s), link and label (see below)\n##\n#\n## Node label with placeholders and HTML.\n## Default is '%name_of_first_column%'.\n#\n# label: %name%<br><i style=\"color:gray;\">%position%</i><br><a href=\"mailto:%email%\">Email</a>\n#\n## Node style (placeholders are replaced once).\n## Default is the current style for nodes.\n#\n# style: label;image=%image%;whiteSpace=wrap;html=1;rounded=1;fillColor=%fill%;strokeColor=%stroke%;\n#\n## Parent style for nodes with child nodes (placeholders are replaced once).\n#\n# parentstyle: swimlane;whiteSpace=wrap;html=1;childLayout=stackLayout;horizontal=1;horizontalStack=0;resizeParent=1;resizeLast=0;collapsible=1;\n#\n## Optional column name that contains a reference to a named style in styles.\n## Default is the current style for nodes.\n#\n# stylename: -\n#\n## JSON for named styles of the form {\"name\": \"style\", \"name\": \"style\"} where style is a cell style with\n## placeholders that are replaced once.\n#\n# styles: -\n#\n## Optional column name that contains a reference to a named label in labels.\n## Default is the current label.\n#\n# labelname: -\n#\n## JSON for named labels of the form {\"name\": \"label\", \"name\": \"label\"} where label is a cell label with\n## placeholders.\n#\n# labels: -\n#\n## Uses the given column name as the identity for cells (updates existing cells).\n## Default is no identity (empty value or -).\n#\n# identity: -\n#\n## Uses the given column name as the parent reference for cells. Default is no parent (empty or -).\n## The identity above is used for resolving the reference so it must be specified.\n#\n# parent: -\n#\n## Adds a prefix to the identity of cells to make sure they do not collide with existing cells (whose\n## IDs are numbers from 0..n, sometimes with a GUID prefix in the context of realtime collaboration).\n## Default is csvimport-.\n#\n# namespace: csvimport-\n#\n## Connections between rows (\"from\": source colum, \"to\": target column).\n## Label, style and invert are optional. Defaults are '', current style and false.\n## If placeholders are used in the style, they are replaced with data from the source.\n## An optional placeholders can be set to target to use data from the target instead.\n## In addition to label, an optional fromlabel and tolabel can be used to name the column\n## that contains the text for the label in the edges source or target (invert ignored).\n## The label is concatenated in the form fromlabel + label + tolabel if all are defined.\n## The target column may contain a comma-separated list of values.\n## Multiple connect entries are allowed.\n#\n# connect: {\"from\": \"manager\", \"to\": \"name\", \"invert\": true, \"label\": \"manages\", \\\n#          \"style\": \"curved=1;endArrow=blockThin;endFill=1;fontSize=11;\"}\n# connect: {\"from\": \"refs\", \"to\": \"id\", \"style\": \"curved=1;fontSize=11;\"}\n#\n## Node x-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# left: \n#\n## Node y-coordinate. Possible value is a column name. Default is empty. Layouts will\n## override this value.\n#\n# top: \n#\n## Node width. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the width. Default is auto.\n#\n# width: auto\n#\n## Node height. Possible value is a number (in px), auto or an @ sign followed by a column\n## name that contains the value for the height. Default is auto.\n#\n# height: auto\n#\n## Padding for autosize. Default is 0.\n#\n# padding: -12\n#\n## Comma-separated list of ignored columns for metadata. (These can be\n## used for connections and styles but will not be added as metadata.)\n#\n# ignore: id,image,fill,stroke,refs,manager\n#\n## Column to be renamed to link attribute (used as link).\n#\n# link: url\n#\n## Spacing between nodes. Default is 40.\n#\n# nodespacing: 40\n#\n## Spacing between levels of hierarchical layouts. Default is 100.\n#\n# levelspacing: 100\n#\n## Spacing between parallel edges. Default is 40. Use 0 to disable.\n#\n# edgespacing: 40\n#\n## Name or JSON of layout. Possible values are auto, none, verticaltree, horizontaltree,\n## verticalflow, horizontalflow, organic, circle or a JSON string as used in Layout, Apply.\n## Default is auto.\n#\n# layout: auto\n#\n## ---- CSV below this line. First line are column names. ----\nname,position,id,location,manager,email,fill,stroke,refs,url,image\nEvan Miller,CFO,emi,Office 1,,<EMAIL>,#dae8fc,#6c8ebf,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-9-2-128.png\nEdward Morrison,Brand Manager,emo,Office 2,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-10-3-128.png\nRon Donovan,System Admin,rdo,Office 3,Evan Miller,<EMAIL>,#d5e8d4,#82b366,\"emo,tva\",https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-2-128.png\nTessa Valet,HR Director,tva,Office 4,Evan Miller,<EMAIL>,#d5e8d4,#82b366,,https://www.draw.io,https://cdn3.iconfinder.com/data/icons/user-avatars-1/512/users-3-128.png\n", "download": false, "editorTheme": "dark", "editorUrl": "https://www.draw.io", "enableAnim": true, "grid": false, "lock": true, "name": "Main", "scale": true, "tooltip": true, "type": "xml", "url": "http://<YourUrl>/<Your XML/drawio file/api>", "xml": "<mxfile host=\"app.diagrams.net\" modified=\"2021-08-27T13:25:09.515Z\" agent=\"5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36\" etag=\"OPnkyx6uK9qjdHjns0J-\" version=\"14.9.9\" type=\"embed\"><diagram name=\"Page-1\" id=\"38b20595-45e3-9b7f-d5ca-b57f44c5b66d\">7H1Xd5va2vWvOZevB71cClEEokiAQHDzDdGbAAES5dd/4MQplnK2dk5sKwkeezsyarDmU+bzrMla/4GXx46rDmUkFZ6f/QcCvO4/MP0fCMIJaPw9Heg/HYBBiPh0JKxi79Mx8OsBLR78zweBz0fPsefX372wKYqsicvvD7pFnvtu892xQ1UV7fcvC4rs+28tD6F/dUBzD9n1UTP2mujzURAAvj6x8uMw+vzVBPr5CefgpmFVnPPP35cXuf/pmePh5WM+v7SODl7RfnMIZv4DL6uiaD49OnZLP5uG9WXEPr2P/cGzX0658vPmnjd4mcnR/69oGodp2TWjnSER/D/s06dcDtn581B8PtmmfxmbNoobXysP7vR3O+L/H5iKmmM2/gWODw91+QmRIO788auouqmK1F8WWVE9fwAMPP98eeZlgKcjQZxlt14ZVgcvHq/r5bnnYYWp60v+PAoXv2r87ptDn4eA84uj31T9+JLPz76g8dlS/w+CSfIJQj8dbL9i//K66BvUX44dPltb+OXTvw77+ODzyP8LFMi/DQUMwq9gID4cBhD623AAIRi9AgJ7usYB/Ook30LxzeFfjwb816FBIPAVGvgtt/gAOF687r/BkcXPQ/H9WJLfY1L6VTyekT8N3ZQ9nXH4Nl+PUWUR583zyaPUf1B6PFKcm+mDl19y/4TCc86dMH3+0C+5dXrKLY6xewPEz0AFRd6wh2OcTYNs+JV3yA+fD38mJiN5+fT3N/BjBAFS8GRTWRzm4zF3xPn5hK9sinz++TUmgbxOVwgA3oyTCHwjUoJvFSmhOyLldJHxSLHEg+Nnm6KOm7iYBs4ZP6s4fm8UL69dfB7cpihvDnV0KKdPP3bhxEOfgqwoqjI75E9tnD8bwDUamEv4TnBlleC1h3sHnwjc6XgWl6uX14yPjc+PfwGe5BWe4E088Rtowm+G5h2RdkbzBpow9pBwIjOcP1cbXBUHCAh8PJzoDOdPwYkCD+mdd5TcM5w34MSB1+QYAaGPhxOf4fw5JoQ/JJzEDOdPwUnA13A+QLC9o7M2w3mrFQGCj+ie8B1NiBnPW3jC4COSIRic8fw5PFHiIfGc+0I/iSdy3el7gNoTnjtDP4knfqOX8AD+ObeGfhJP8gae0BP+0XjOvaGfw3ME9CHxnJtDP4kngN6Ktx+O59wd+kk8Yfgh/fOO9tA8CU7/4knwl0H+YgrASzH77Qw4iBDXlvBmM+DIHZXrn6VOgV4phUBsLFA+WLGF3FFv/lkoYPArZwBREv1wGP4+qdaVMmfEAb6Vnz5AqoXcUeX9YXAQL8H/GziQx9AxInfUaH8WGhCCvs4VD+Qcd5RYM4X71RQOuTYI4lbWel8ZIzKXZz8HJ3nFBYFbcL5rbYbMU/c/ByaMPSKa88z9zxbL12jiH43mS3NuRvNfookCD+ib6Dxr/3No4sDrKgUDyA9H844eyozmTQXjI6I5z9j/HJrEjbx5K9IS8HuiOc/X/2RDCAQf0Tnn6fqfhBMGH5EHzbP1PwknSjwinHM36CfhRK7ae49Qc879oJ+EE7+TCr0vnHND6CfhJG/N3H8smNjcD/o5MEc0Hw/MuR30k2ACr1ffmYqUKzDJdwVz7gb9JJjwNZgf7pl3NIPmuW7627nuqmgOn835/0jg15jG/8E49Mo0SPKGaZDA04tg+F2mv7E7ukuzeby5eUAAjj+9aLa+sRD84y0E/OuUZNfapec12B5CuoTd0T6c/fU7f/0FFoFgr9eq/LIs7LfmgL2n+hybRWz/Q+T+VYEbIeGn18ECQm6K4ol3zet3NDJn63hz64CBJ+JV6AAJAHp6KbG+sY9v19Z4FxPB72jATOtal/cPxJfVuQ/OyycA/32ArpYwuHVfD/gyB/o+rvNoTWNvfEAX53FIN4Wb+uP1U8/Lrj+vDXCr9P3exl8c6Xse9V/S43+3ln82/o9D7o7+8NdINPn4PxDX66H9Aet8Nbjs88+PkPj1I/5FYf0EYjCCgShMPP++8iQMfsJwGMAxEvr0+z3jzaO1ld7br35CK3ZXdERu9JPeDsU7+kkP6WP/fvRh8ora3Rp/7Avbex8AHk1A8vBuBGJXC+vcZhnvCuMd5duf4UdjWYzcMfzvWjzjc3n0AOURiKHg6xD7OOXRvM7DI5gI+WIL3xgI+hgG8mhqlMdPxCSMXc1y/CAX408vW/G8C5jEHc2QPyQdk3f1XG5NUr/d6N8hH5lj7Y9i7S+bhQQJEnyCXonmR1PAnvDreAuh0BN540aItzOSO/oHs5H8yEh+1UT1yNmeSOxVSibR6wACA8gT8J7dPxC8I4p8amj/L1TlX/e48asdYmD8RsbDbgVc5O0G64O6cdck5Id8pgiC2PX/nxsd4uq1i32fi79zuH/0rvHS7zGAfzCy+6Pqi56vf0nA19z1BvYvCyq9AfQfdH/gL4Lei6vR+z6dTuvXzW9mDdCDGcMH3V44xwH8w6H/oE7yHAeu48DHG8Md/egXnKpnFvyLHfHKTr6xrHeA5TMO0L3MDHg7ZnZHb/oDaCwIXPUNHoLHfpBa47fPX/gPDOM34rEf1ID9E/PXv7eGB+Ox9+xN/cDG8Dsh/2g8Frqj7/PA0P9RceDjjeGOvtbvx2Pvh+VxeOytLcWX0H8WwEOwWfj18iMPwWY/at/u3z2LfbG135fNftQe339gFvsJa3g0NvtBy0rNceDjCcwHLUE1x4FHZLN3dLd+Ozb7L2B5IDZ7R7PpI3js9T0ij8BjP2qT7t8+f5E/MIzfh8d+1H7ef2L++vfW8GA89qM2A5/jwIdTl4/aN3yOAw/IY+/ZdPz347H3w/I4PPajtgt/A7fMi2r6tP/dOO7n5z/WRQPY193tvkrnbwCO3NDMvyERf2RZzy8Zd+LWuN/Qo3/xqfdytN+7j/MITgWS5DW4IIHdcKr3zWa/t+boLajNm6GNY9ermLwz2n+OzOiRMiZIIrec+8atiO+bMZHfu3X1Ldx1cX4YuCEAAR4S7jvaVb8zQQLJG8QUJJCPJ0jI4/eGmiLO/Ov8+OObPf91HwAk/t0EwX+5MZN8NREAYhh6Yx1K9AbKL6tFvgHIj98FenuQp5vpf9UC8/BrfgRgHw3xvIT4AyyUMv08kVcb3r0c+W7VauQJgG6F+jezkHnR6ke4cx9/nSB+sNQSDLznMjog8teshDatAX213tWttRPedfEVELmjU/WnAADe6QPvjMAdDaU/BAH43ij0vgh81O70/4Kn1nGe/j/o9+Cp17mGvFXp34AYfqudM8CP2rL+r4GYwK5LkXeG+PFbCr9lq/a1xnD0ZuhGa+F9O3f3bHP/W3fuoKtRR26N+nv37T5qQ/pHdrI3WY0PuarmiY/ult+ze/0j6Hh+NBPyJm55a4ry3Z3y975h6LdxSgS42u9pWt/8Vp/9RovtDR3zjhr+D3dM+PX6NI/hmL+3VuQR6OeXzXG/3uJyS1f3rqKQj9qK/s+WAF0jDUEfLf/5qH3qHzmz/goadbWaGHTjvrX3ZbcftYv9Hy79QdDXu6c+ANS/d6foEWCFXgtOHiAt37Pb/QPD+qhp+QrpB0jLv3cX6hFghdEHdOA/5/akB3Lga6QfwIF/747VI8CKvFYzPIID/963PT2oA18j/QAO/C/Wphk/Ly5r/127jr+C+FwvrgJcOxhEvG9L8Z5trm8sRfN//1Zr/u8Xo0Ff5xmQeIDFaO7Z6P6BQ9IvnQf5kQ38g6Xd7THE96tQ3JAfv2+Qwv+cls4brELx5ubwqnAEP9oa/vKuz3tCDz1aJJhbQx9pDvBHFyb4b7IEzdsh8TiL0OB3dHk+hsHCV3dRPgSD/ct7JT/vHl8s7fdlsHND5SPN4dEY7F8uJ3pP6B+Owc6ao480hw9nsMQdfazfkMH+CyQeh8ESd1QTNxjsG9NX4rV+DsRusNdb7Wr87Ubq957R/ZUh6t94xRcD+xd35L3C/qPD1SMvPfn2qPw4Vt3yP/Tt/O/x64e2qNL6BY/vjSDI4tL4gtnv4org97eI3GAOt5boebv+AfH4zNGr/Lp+fteb3fv8r9tH/6UovLo17+ZaW1/myL9bienNbn8m7yCIHw2zX6f/zy2q3K/e4Eb3KVqsPp/DS+T4sTL6V6+w83Vl2i9yAeBloaVvjILEbzBVEnozo3j8GdDKD6o49KtDU7xpACDf8O6za/BBAoRvxIQX5fl3IeHNIv+LXT0y+oc89N921Yu3BB4CyBFm7LXj31ju5Bby+Nslg8ef75x+/fqFF38Fpgj4+pYVmIC+LKP6bUl1I5i/LDTzBpg+vujdGx/Qxfm5YfHGDv0rgL5WRoO3lsa91V0i3sp1x+DxMTD/2FGr+B9XLPzeSKblDn+0ttgPliJ7C3gRCHh9u/7Iye5s5L7ViqnQCyl8GHw/NxjfEt1fgCZxveLUjbUTv1Tb79MJhoAPyrM/BPNLG/NOMKEHcVUQeZ1yQeLpRbH3ra+CN1LuW5VPEPBBKffPhxfCsI+H956NSr/evPAPq24e6vLTKsNB3E2s5xojzCV8J7jCCLjGyDv4ROD+msFHXqfAcZBvVKbEjTuwibeaHhjP4Z9H/obf/NDHrlwom15JHdw0fKah34xt8PzzQ2e89p8rHAGAANgrTusd6uhbtvudB8dFjT/FbpHXT37vf0gCxUbUydemAN4yBYR4eqk1vrUG6M2s4Z6NQn97P4RfdY1v1hpfl4j+zg+/HH2DFHfHRN0f6omRf6iah/FFEISeyBvOCDzdSoow+vQyr/MG/ngHq/3t/RG86tc/hkPes2njH+qQbZw/v+kDPBK8Xpx/9Ej4VtcPhm6ZxNejb2ASd8zif6w/ftutw17+/nxy4C+AByJeFxA3nfWWq74ZKvdsYfVi7PHxEPr3eOtPO+cVrM9fuXg5CrwcGR97h+bwH3jx6U+ILfPwP9AyNihFbYE1FxaL8UfWdhGzC8dHtDT+otLlwpqOV+swysZ/mUWaMVtDRXIFci9mHW/VhJu0LZRbs24WqbaQGfohtnuVlRVGAUXmgG95fjwuHHQe3fE1tUjNqFimjKVa0FJLGYNP1R2/DqHlmlloKsOui8goZRJEojOBnU54jUtD40shQfC0mAz1ePIoJGewd7QUaj2mc4Ky0ZDfxsejoo4XRYWpOP5eGpXcjP/CQc4JdndKaM5dqcgyRrIDF8MUwWa1qVdkB9Enzz0wi7JJVN1fIcmCqCuu37g4RARbPNjWe1dSOb7vqpry9120BnEfk2VQoWExV/cUngrWmUug1qPAlbWxoRQsqDFNU1Ie0gyky82pyXfgYb0XXMtDL6vxCrRlYW8HVvONSmlAj46Bqiq3FoMPK5yg2G7NKNYePNMk7srA5fmCtEutR+U5TZ2EPjHAkZVPfbYeP8vBzLRVET8vL4IVNbm9KRGkoZzewCuAaJvKHM2a6tYn4kIvkWwpTueGl9l66PbH8f0+OFoaNaDwvlKx9W5RUOMYLaVxEDBHMxGr3HvkUioZkJQuwwk9mBF1PEv6JqaPMssUh22kMNlyJ/PtZalFSya1/Ja32n5DAkQinIJq/I4WkExjMbiDJPBobQtAam2jNZf1W4l367bfaeY6C0nNwmlxTdahfKYdFqyPhb5IqdXKP4HReI4HgWPCU6JGuH7yBsYXKXtpuRE/foFpuPku3Q4nA+gBjQ4uZ/S4gix2mzNg5lFVQ7XTdbPSUck36KamAd1q/aERUtcMlGEJbTgYxy6AxNjysD4xbUVPlsYIkbkY/xW3R5oIt/Gew6wtt66rcyLJLQIybRpay0BGrcPADZom7YZlYzcXjScoqUVjNRKYlkmdpUaFAoEZEVbtuY6N94AuhrokSp481BqxsYID2AlHrtbGAeIW+aCaHNcg+LYVw507kgM2cBFuxOzcnkVVoXSxXSziCJJ0T6+7aHlJK0eXaBpuSMtVmjUwmul5d5K7Zd/vMxJxGNGqu/Bcwj7Eg+1F7IfAbTfoxTvCNLMFXUW0mS7Ej4i/WnjdOgBAcyWM3s8nRWbvcmh3hiQOOB6hc0G6k8e3kuoldGkwiBVzFZhvjRMnn/DN4lydW7O03KQ5YyLhD7JCA5tzqapl45Hb1mmJS1usXC0nnSDYSULApvliC3rbtjhi5qKd4vJhfGGOnezGLhtEW5xLffxGsSwsaqEAUSL0sQLBoLrwTkcuVfXYr4cSK61dtW8IIQKFdJC3A2cqEZMOwzqs02RLr1V26wz+ZkKVDe1koBcHaQpebCONv04535b0glrg1VAceFgSsaE0ot5qQ7oY0CDaGNpGGRMSxa2nz16NwYXNMV/WkyZahXrt5aUbapYHEXy24esOTHaokYMplLGrPetSYlFnLcieG2b8jIa7WEPE8UnugR3HU9PlHQxdMRq6GYgt4ECSDEDKqrHNteFFGxiNoXXgrsIt343x3YXYao+sE0kcQBpVAkNat7GCwlOwXjAZq6faeXtcLn9REQuTT69nu78sVf5tH36sWq5z8Ztp3KB7djF6UWT/d65ynV+Psec9T5BcMen7B/Rfi7lvLDV9c6BJ8AnBgK8/N0adfLO+zT2b/z0AL/0G29fYXyP4D/Z1t/QXv9VjRW8Wk+jbVQ73bK/2B3BUduKmlPqJo1L8DmQ344NVuXvhqKC7MeuTruqQ0era4cCxxsUzocJYMHxTCgpjSjugDmt9WVnbHb/dNmysWtxCFeqeKixVFngwYbbVGJ8FPqYYJo0o9VQYjp47Ln/Ghiqou4k6ESWOrLMYpjebVT2x4apLA1wMx7xC9fI+PzcDekgVzQ9KLnCgBKP3Kz2ZWJgAZF6COls61iIRJvk2WqXJMhDVbiIrNaL5sCi18miIVGotdYtw1NFsKCKtPWNtIfJu1XR0PzCeV+BWvw8nPphSyywb6RLLLzid8LbqZn9Ym3zbraje71FxPSUxul3RNNecz/2OUH3UJ3cjF+18WL6c+5F2TCfebno+zqzAI3xQYcTmUBX7bRzpkUYJZo8bjXIGmNr0c9+AUiKN8wqC9T7YMPK5abaY3h9kx2nOSXYwRWMbN9r4oQtJGH9vcKw2Wnjo1qJk2chEy/jdSR9iYnsRyUCqa7Pv7PBYbuQoHgyR3DDHGm6Wa3BpEpeqs8vTJo9ZoBm9kaVzfJgYWuzBzgaZuGAw/k/aWWmgSZOIusSYuzxz7fBkYkpaWjXEGSe92NCQAza7oVuhk79B7G7RRmO8kqNoWYaXJI8Zszi0SbkRFmuuWe8BYnuAjUK1ylNpspY9XshJCPYgKqGgx1m7aej55ch6SdrPZFmUHKsZz5mVWavPFryamJ1XeGYpJusxOFO5mRtlPD6w04V5WsISg2R1sLHbajWOsorZnVIIslMCfLGzMQ9FEmOrYoOmLxYNv4RG091P1see8wPGGOtdlDNYGLFHSQKMZO35As/wZHDg7RCkaBFlk/3Sk+jK5RHW1aFzuNnHOr9sVFryTL7rqjJfHGPKPK5czxSEDhGKyBZEidnXJOluPAHzQw02F2um5gWNrUmJqKXdYToua54MrvlF0C8oncdYuwLkicYhvFHXYCBOZ9lWE40Om7PjQwWknPbQ2WTjvjcUadiuVVRsIaUogiJwKb5+HkmqjzXpMD7sV2q7zXlhO9ltKI9H5C0CLOrdwO1ImAx4rTen8Qw/jQbOX7ZQn5HBPk2aZpILUFGlYWbj9IJpsjAETx8RLFeY0mVKtUzYE79BaatOqRL1i3430C5dM1YIh96u1hO2a6iLCpESlLLD0oVPywvkuJupxFyfjOi0NtSjVJ5Y6yAp/caiUacVn681XwsQmZSlK+ccJyGKaTE5NgDS2R8ob/DhxRkMkQ3tjq5WKWK+n4zZjyQiyXhjOuushDar6ZpNMFIqWUUCzu4JchxBwmgt3HCXoF3am2VtTpdfGVWyy09jPGl2STdlDlXFeV7R6oy09u4WuOx1uJ0+Deo9X2SXdEs1GcSYSCmh20xdnOPFkJ34zJ+qxXZVwoCzEo7wgrt04AGR1WyDOCuD2Q9GrzO0IzLcxccXaSoXY0E7EdgAWGUYe2lhExsoyMx9jyMqjlkvduyBL4IjieiCDtBDa2l7xBpDDLuS+qlCN5tMCXUCsYrqOYah6hq2VYa3VYNcQ6nLWUg6vZdZ8krIkpVDOo4rjBfNFr0zpIWwYcNWPYOnc+yPvk+Vfupre6E7ahmyvkjap3g3vlzwrIRmAAyCw1VWMnGXQyp+Ou08QVZNcIhrWxurlwgGiWn4hOYs8dqid1YLlI0cDt2Pn7NV1ntzxYJrNVrGCZcDBsWK60tmNsZ62G2dQ7Rie50bENupnRNArzl1ZCUxW4s1hI0M7+Jke1jcJ2OkK7xtsNRR7OIzo8NuYy5d92zsKIlbZPpKUezRi1uvLMavZbkUSUONqYsKpokuX2KRydV0OtZyhZoiKwjJs1V24nQHdZFo2yw5uT6c4to8HospdZDapm9zL3WPi7V4yqKpoFxr4MlcZphc262wzisy7LrjGeDroKObfVdIvFor4+um/83T+IUm4WiRyleiZzvDxVw0RWjEQ1wpu1PQBrLfXFplLe/8yb1gSCkXjccvmiymvU11SKtFyaUNpeSAjKp83on1iC8g95ZWtNQa8vhYxw47J5ZsDR352jg2C1SOmxwixivfHjuiPCh+TiwmS7Njrt5xnBLBxwtgiaKHLBs3KpIpZ16KyKlJnm+VxjxFZrNkydrRwOEy2tg0FhcgkdDsHLdVg2t5pavkperL4ynzT2PJVU6ReSCnD9ot0a6lIcARAA0d5FUOhqVs1tHJC9bs7pBqW5EzN/ZUPx4QHfAKAjx0e/M0fgA4hS3hWHRIRaLKGGaE9qiNUR5rBdLgszUOS6xoTQYcVH1zYbRyNX2jszIZHENNrijB/TmHCbEoSw+qULXEYGvVwl5Hj0mUtfYWGxwFHV+iSk1s1HqtAvHSHb8Yi6gAXWxzl1Oz9T5VnCmk1NpZ4LgKxQzYSKmNkqS2Du897qTl0UEwZVbUukOB043uhsyiFkefa+xerJV6qeaILjvrwt90y+ZwRMwT2lQkeB7UXSHaImz4O40ipljYb3gOOZ7QsXafgtF6/LvU2NMUvkdzAYDdsZPbLFlnKRI5+41O13tbdAJmCblKoyw2AreFgSlx5xYAL7NLZq2hiYWY+NT42TdWAUq4fU6ROhTr/MKDSwwMNdbdLoEV2erhaer1CenBCleF4kMNpTmrwwlj10eewpb+nhSsBCtCthA8d2N4q90urRersayDC1HzttoUMBd47YvQZbunGFoAlEE0uoVIjFzB1XJ4o18UTkM3O4ZoxGpo2kqScXcVnaekhF6GjhZ7SKksspZXy2CVLPd7vGWRlI184SyVEp2b5ILuW59B4fGNJoxXI3cSWLc5nVVHIdeODiLU1isu7WHZskvOZPcZdpm8b2SKrJGnlz4naiPP1wxq5912IsOUoO5QpkqFMAwnMj799xblzednUeIJRb+pO6/vN4WhJ/LWxOVb6aOhe7YE/O1nLVHk1UQISMDkE3itpHpfPc892wL+oZOWH6XnQTHgSkQAEsBNY3h3Rc89WwX+/r4IAU/Q6/3gcPiGL767iOCeHQH/UH/8OFUPisBPr+epQRxARt8jv/zcWkfo/RU+9+xg9gCd2zdUFMAA+gS/wopEb/Vsb3Vs3wqXezah+gP6tbOmYNYUzJqCWVMwawpmTcHIDoAn8ptWAnCVlm/s5PveAoN7tgz8nwUG9w/mv9cTXG1cTmLXAsoPVRPcs33fA3DSX6Am+GJMv5ma4J792f4AdjqrCWY1wawmmNUEs5pgVhPMaoJZTTCrCWY1wawmeJPy5nHVBPfsUPzbz2CCEI49EejVwH+dvcKJJwz/b7NX76szuGeD4T90XvOjdAYgRMJPV6oTHPknw3h3zcE9WxL/CR4LPcH/xWMJEvqyIOmHShDu2aj4D3XVj5MgjM6KfnG7rzYxrU8OPJoG4Z6dmR+g3/uGGgQQRIEn8EpBRNz24HcVItyzWe4f0OqdhQizEGEWIsxChFmIMAsRRvIEoE/4N9waep2ZSeBWZn5vLcI9m5k/shZhpD3Qf+06ECR5a5w/VJ1wzw7iD8BWf4E64Yt5/WbqhHv2yf4DKOusTpjVCbM6YVYnzOqEWZ0wqxNmdcKsTpjVCbM64U3Km8dVJ+APvyL0r+jHwBDyZQHur6sgEuCtVbnfV4eA3zHX/IdObn6YDgFGwCf4ahlw9KY1vLv4AH/4G6p/TYMUv+WQN7zx3eUGxB2zyX+oR36k3AC+6ZP4o+xkQtxxP/0DdG3fUGNAguQT+e3Pa7AA7GY6fVe1AXHHjMYf0Lqd1Qaz2mBWG8xqg1ltMKsNIBJHr7dSAJFbyfi9BQbE36CknnZGvdpiAcaQp5eL/dhyZlZPv385A0EY+QS8ZsgwjD+B2FcCfS1M+YjKhvzr1dPQl90tvtna+KMrGRKaK5m5kpkrmbmSmSuZuZL5OyqZkfDhT/g3N4Pir9PyjaXG3rumIX9z0fRYG17tVw1ds50P1UiTf41GmvxNNdLkrJGeNdKzRnrWSM8a6VkjPWukZ430rJGeNdKzRnrWSP90efO4Gmnyb9BIQ1/27vtmEhm6Jb97X4E0OQukP2D2Er7aEQ4CiJvW8N4C6XFM/wpvJF55I0iC8Hf6yw9eQRF+icyzY76fY8LAtWOCBPFPhvH+Pvo3rHkKwThyY9tG7GaD9r11PzDw9y5w2sb585s+wkMRALy6sQXEEfDpxhTLSGRvGMXXo29gFH+FHg/AiCfglaIHJCHyn9Ln+/vorM37ABcFSPAJe+2iJAB9t7vqQ2jzYPDhue6ba/MA9PV+Y/jLcmofpc2DXzbG/sMnP2dt3qzNm7V5szZv1ubN2rxJNgb+N20eiIP4VVp+Z20eDP7m2jyQIK7YDngtefxIbR4M/i3avK/W9Htp82Bw1ubN2rxZmzdr82Zt3qzNm7V5szZv1ubN2rxZmzdr8366vHlYbR4M3VGNPnTFjyPXd+M9WMX/MnX951f8X6zpN6v4oTvEHnPFP1f8c8U/V/xzxT9X/HPFP1f8c8U/V/xzxf9XVfz/orx54Ir/4TXMb77bO/ai4/72Vr0bZeitIvStYIHvkKr+ASXorImcNZGzJnLWRM6ayFkTOSZiDH7CrunRf23lv/sa7L9H9/4N+RJxveAhCD1C3/7LTd5/OGma+/Zz337u2899+7lvP/ft57793Lef+/Zz337u2/+SEpTAvu/Q31OBvmHL/o777/5thfmqaqQXDMEub1aeS4Kh2Pfov0P4jc1qAeLmIi8YcT3SL8f+l5EG71h+6w8oHQ3gub3+ud++Pa5Nf/xXYPNyZ65UbeRLobsSLvYxGykBBfh7KuM5AzmY4+tXyaGyo9I7Sy1d6ON3EZ06UBSfnql+hcHNEA75xIYENe3gg6knZXMBw17YSeEyDnfW2flUblQXKYAjFMck4ZjztWSUfmFt48yW6wF00U0JyFNxGXRAznln3wPdgTCSEks2E2lK/Qj1bGMcwZzCo+Nlg/V2YPRqEJ0QkaNRP0hINEfIlQAe9y45hmFwaaNAWiv6yENilJ7yTdW5yh5bwj481FMQd0rEZDLm0BcK0W90BZq66kPe4VOJ5nsEaicoOxVwyABz9b4ChtUm6jlNCnXyjK8OIEnLlJ5yZI4me6zywISkVOLkud74/ngkEJY9ZcZNvARQDwzgT7xxQ+MWPEAa2ja0szrD+cgkKnu1P56D86GIGJlFbG/KbknbtrAV6wgC+ao3xfuNMlVdq/NUBiqjtSmoJEOrJkREAF3YarqHV7Qh6v7Ggl2PRWRpoUoqtl2C4MBb7DPTRTodgnpQuFxCKjyvcZ1RPGpJwnty6OAVT1T4RUqRIDFyxoJA6hk7KrGDfTAIEDYYHpK49NGuxtKs1WD7iLg25+RkzyJLxPO5o4pQFxweC4jQCzfb5MT4jkSdmqZEjkc8nkIby3MDOuCZLxy2EpS5VBiqA3hwXRVhnJXq7/dbKNb0POYkruCrHCkkgFktT1JOaYt8mwrpcaISZvfM9MZKDosYyG1aBlgWyYBResAYNSNsidhn9QVqkGq5k81Kc0S1SHSMO9ErjqUDAfRVXRPKWIuPeXqsIMXUZenguy5xBLUN2xaCbe3qY5axFWtQ6K42TPSCi1q5Sc1j5YzPjXW/XAp5h2FkE+kDDgPKUUcWO0hSZJEvkBbL7bTttp1uo12JZtlx7+sHIq7r/Z7LMsOzSQs/RP4JJ1ty63beyY2XIA40MMiG4NoDpTMD5OeEoaVtqnSyzIW8ZiZ2LouhbvccLDlTuzjc5Z4pJ1Fz5hcmZ+m5hiqwfelXgChjG9TEEyFHFwOHl1OZZrfU+eIjgUUfoBV0XiGxZzhRckhgYyooTXskNa4HKhW77kwFO1XW1jqfVeO0HPw1eVAgLoIdO/HpMe5QZ2lrA5CKJCYgb2X84hQdnh/Z+uIP59CjuyOqZRgoZiffg1M2MUBP2zQwbAfuDt2ZrituoLqX8JK0pDO8zyo15zA+CgH5tO0MBOQMW5yq491Y6UeQjmVDImDhiQQ3yK71F5e+3Oi94Z7S/UWpaz/tOdRs/axk2R2VCBRUeUp4lIW+l8bhqpR9TCVHdTxtA8p53Qv3uplTZlEm64Qu6UUWncdCz9qJ5S47BJY51VF1KzITyzali2zZxiLP9F447PXDUPRkxRi8xJbkOMqIbGzAw8HZxm7U5VNFsB3tltwT5Zo82qP1+yi9wRdwqaw85HKEXG5q2hAEocCJdehxTtl6AJDXG9dt7QO4Z8diby1yIyf1+13Z63UUGVntmDIW9+1eDvmJkVAbySz2eRL3grZflhPdRw4wn9q8uU0XyqFlD3KzQiLNn1g6cvKw9co/mWlBNGfgQrMdn1UmyBzd0XwZl2UkJWXt42BkJ7jLeTx1pvoZcE41kdUne7NIKXqn9suUN/nxCY9sCDfkNKyFRkTZPva1ytPbOk/P9OJcdbEg2AhSar6jHQ0mjYBmVx52U1Nsi+4pIelryFsvbOXEH7bIbtComt/tJu536EefnCKwsC1w0tuN1Rs1LSVFFevLyQ+rU3LMVXkFp0JLqLIxPsGy43BSRxDMUWDf7rcOHcDImJdg5SCRogjsqLRBL/SpTTcrHl2mHkFweZNx8QXery/NOHDsNllh1cDjHjgyW2M8mbT3M0Km9YQiiZPoGCk2Be+GDURg8AzNbFpqlQCnerlac2PGzct2taEjD67z/KAtDUs6AM1lHeTWAT2evJ6p9MAmd0IxBnrwouwYboBl3kxb14jMjsZK5bLN0WmulN3FfJmEYYCSh1SVsXKzEDi18aYi9rwN7QgNlZaD6DOSbhinZnxEzmL4mMRbqZq6NH2FUVaGUX6qXyQs8Stut4QiR4bRMY5FAFbUsI1JNE97JUr2mH9Q8bPICFHJyB2yOyzrwTqrbakf6ZODqZBehZlNUKdyxZMcQVJ5tBKGhUSTWE97mhosginHscIazhpnz2T+Zb0ULVHnVe3S7WMbCAY8XbNnOacjsc0tV79MFVMPhJhiTKPmHOhKacJ1dNxuo0AowkO6j1cw0Phn0Kgw3XVXOifuJFFHYRC2pMDql6G9VkV3uxdUCTxyol4sjgM7tTySqte8fpvKsRDnBilbvcZsMxdBjDo7CoWE9ZydEeWZB1wm9fYhM2jMyAOoWgx2Uh+uBAXQfMw8JFZTCpZAUC5BLe0jGYpZX29sCm3dPbEQwourM/vlVO+NhbsuxbszxZpLZqWl+IYCFo4uDHKfxLtlxGkuB144cyrY1g5bGPBwYs9IV7S80qVRmx1bY2eRrWkzDHrGTb1Mk8MY8Ao1YKYC8RgE0iWeek5DHKT7ThyP2d2mX05dHj/rUjtQMbd/1iS4kLKOp8lMgOeq0KedPOPRkxuUxRpI0Np360Q7Qqam6dhwMeVNXK4bWanP7TRRb5/PeiXBFygoJkNiU9Kceu+qTJPJAbIlwnYOF0B1LVaUpGAsHvvdCTfkVAM6XkHcNFVVw0pPJQKjXL/Ng5VMHCirMvflsBg/R5cGp93C1cSCZHJXW4IDg71OTpRyfz7KMLkit34iSIRykntad4fNIUATOZni75S5jrpuYM3R9tkVOHXT4tJLca4VzkLcn7a47cJ5u0+aOOjrKCa1VelsRzu1SGKVZ9tKpMU62MrQdgPpEhNNcYaSWARK0cTnNpejm2t2Pp1nlynJMcjToCnwuN5dtERkIfUMkPuR7G4zVNaCsrNFwus5yZKqHdgdzMLVOSWr9zQg6XThWC1ERbsjtVWtnJua9SXDEOtmZavgGLDyLb7rTgfRoat0A2v7pucTwgYvYh05jdPFyKEOO5gXtCE5gevLCIl7LjsPDEHnjAgJx9QAb5WxFHibhSE1pDBlG6bD4jJB425JUn4U4kdo8PcSgy2DyN9WChdY3sqSq1Wrri6Oo3Jpzsp86ibnKMI2NJsQPn80QoziWSVUaZsXDd70wotUOKrHx8ngWb7ceIE5HAKczlK3AUfDBxPiMLJQD3FCRO4mYxUX+y2+8EQTaZPFsQh0cpKjxEeDz2Q50eDwxJiHLhKijOKc0lcFAVJOp2C1HtS1vrNTglsoa9+0GEMWL4oeH2G6Ly5ZXF1wldB8LlYWBXohp0xHnghtWcr2VN9YarkdCYi2k2l+RfUOZJcOtxtrl9gNbIbUJHmxkoxgRxjauhAgZrXndwJQa5TRgctiV+zWJWuUoCTmwBItAGGZnpNCqFqX23SHEx3jypk+YUt+E1LI2TmWShgLAXJZOxpP+EvkU0OYPW983F/s1kmbacOhFbay2/BT+SDEcuWeyKqNQ45hmN3U5OiYjhRLY93mMgG7mxWI18VkKlNmnRQ0QWEE/HEBtzGhDTptNUgIRxhA8ijeKStzl2nxNN0mdC2E8RsmASFzDw/hfqXaiN0kfKn4U3qMa2uT0Ucr2/amP1LS4pQ66LaxsyiQTBs3NAsDcSLyOaKp1lLjlUm8pFStjSiV6Q9xnCzCRjUTQ9PXajnVTHi0gkVivRcFdHmiU1OijEO8XU15axoHdjiDcSd1TZ/uqKEIqlWTgFxod14BHuFpXo+MoWPvTzMG3REBmillHULFmiRIGgk3VqwCdCDnVAd7TrrxqZ7qIgKIDdqNJ6/XD+x2IeMbTGj6Vlo0DNsNPnRpppJtS/Uste/VS4hQC2sNitC59vEFCpGfYgzoEZsIYPsKVAa8nzRoC85Km5W6oC8LaqyQWcE0J7sZa2OXiWy3XSyW3fS6Wl9sl4ecmoxtsfKYTFa3Qbak6rU0jr+/9I3pPZP249NkLHSGve1edOwS5xDH1UX8NFghszqs9uBiZ5NhGjoGtzN83ce1pd8GC8EWUIpcnLKNxe3tkaejW3tLn/kDeQTTRWegJdQ2S6lDV3W6FRZMnviHcqdpO2C5B6eqMBhKHaGeL+r7n+WCDpSVrp/D+hRB6TTbMRWCe0Xb7wiVYPRB2JCSW+7T8zGvIn+aqN1DCZxe+h26pIVBItYST4sCTeRno8tD2T3AzrqgS4WoTxKrVipPxLgAwXvG62EV31m8TQLWwqQ8Qgcb6FI1xohmfUp8gc67YCE5SFwJO29jEhhxOh8NdbDXrj0cYcqLgb0/2T9AOvvt5BDbppc4ElAkBqHl8TzTT+2E3JtYNo5tBhdete7zlPEChyd+ucSIXW9RZ8KRK8w9heNTLHRgnOkpElkIDip2eD1NM+BFcybHSJwrWw3ql4PwqULHYFJGZLnZNGIeIpPxtJTZVf6GxN1g061g2CUgpofFdRkQeN+1sSjuhmlQJ46Gk1BNUNMclQMFKw0CYVwHM4+Ez5wYWsJ0ovzoqLGBytMES8gR9pYtDRZaIfneljGCgKauP3va5T5clScYptTAYlYX77RShwKsTXDHabnmMt5UTdf9ZHaL1XlllGcSCOAydiWwDnfgSHsdQ89IWE5VwMBhiQSiNtxCcIOuJpTEEx72l9aE2inoOOdG9kirZ3cEkVnrAPem07u4coNOKXvqMi22XQo2rnGYuExN2HVrhEtIm3r0l46kYI8N7PAypepxPI92CeDwyEAoYLtfyAzLsMBCpbyFt2C2+y2tUtMf08PVlpqeA7a78Y/pIfXphbfes6OAMEDqNGuz81iqTWl8s2RCapMoYAmQ9mXqNpG0aWxxzcV0nE19BYTCPeAHVOIJx+rgyxBEOqRbndOpPSiWZ+hy6bR0CanYQiRNut2NhVxToYprxpVK2fjlxOVVt5Pg8SimW609nhGSFQG3rAixWe70vU+Vq6itYHHlyKa13ftLYZUY5nmpEA4kSrxYLbTM8ia1QnuGBHvr8auVuT/WruSdQTSiPJ5dKTm8NhVGcdCNJB6CYnHxJ1u59KcaCmqvnGKn1dkewl1Mv9ecpsJPXtXH01UsbQA7NFEXrnLOia3WUSkd30NgEiUNfEYlVyrChjhcFoWew8/n88mLLmCALbv2TPhCJ3kSEp5d7tzWLu+dSVhIooxh8RA8hBouuQqLLQRdlm0L2SuXGm3HuhNpOS/wOFKRnLGMpsZovJ848dA6NQzgqbY3F6cz2clLGcGBrXm0FAZRKdcaMDxhWDDciEp/4vG9v6WynQtpF0rwrGU5HhQPJiS3AyrWOmd2k7NG6AK/eMLkGuuiKiUivUxV364fTFu8pF0nrZ04Xo70utg0YzFkiYs1L/JisYiX2AJb8OE5XMbLfvnpoRIup+eKsJ6Ojw/pTy8c3zM99/U9lNOfXGqBNQd7k3b4ZGAuEXxqjOWcNjkHIACh6SPaRhgA8rCnSagV1ydPpD/PJxqmttrV+bmmTvUUEfMp60HTs1s4sJu08rKdChwVdRWMI+whND7G5EZ+bnE1TNPii7xXUY70Ima/H60dPjNV34yITzz9hNTjIxYAnQuA9pliSOboUU7tCrpxucgQ7PldPw7xpzPOJKk618u8GqP36sJTx8qanqFHVyNDLu63l4hMIrjaJLmvSQasUeZUegcEItoMIeJKXe+x0aOPQl85Fxdt4o1KHZwC1gu/oWscyUogHJ1/4grWqcaI57pntDjpknUUGYyWrk69WFGhPrEmJSsTKR4dPdQuq8kyz/RF9oLzfrI2aeW6RXjmqxGiPWZ4Xc9h7So6JqkMhCpnqM5OOBNTmpisaZ1b3rNEezmxgaxbwM5BwXhRlwzKNPQqX0+danm9Z9ls/DoL3zfS2RP0dLc3qdP+BJajs7hLG0Kn86iMo0agLHx2GkE/HSBgvAqYLooYOu0mezTxfbBHWcqzx2va5BYIjJ5mC7l2OI72Q6LplBB0KvWlc6w8q333STvGikWH6HChOIdhyoj6cEQm31w7w6XHeGwabKWOqnVBliJxPKshFtYTYvvVMkArfF3AI7+8jDkTtw12LHOECBiK0zi8vskIIRPU/pTitVxQWc9eZTZWerI5rZHOEuuzN/VVxPWYvYxco+whqS8ZbxqOSGyl3Jmi6cUe3O3ZoyjP4no8Oy5Au7rtJf/sWefF8wtf3sNe1mXL0H2wTsBJpTpGMMQd4RSLphKI88bVJ7tiy5ZEz9C+GNCCAZ0BVYi11Zzys0qsi6a5LJbEij4jObGH+fo4xqS1DikGpBAoOE1FT8BmlrLAxzBzGLAhGglisqmW/QmBd/6WzXZ1r11UyrficnMm1xC42WNafkjrZryIGhvKbA31p81BWzFkWe92e01Yk7XrSrCRRAFXiKLzEj2xwyT3XVuWISSUx7CbxBPdxNHE3B0LsrqKl2ibIWMwW6CcLwzDScPa8XyneA3xsNOBp2xK4EdmKC6Tp0XtAC0yd/yi2CbO67WYL/eFa0koKAem/ByK0xxSuJNFGFMO0jgyoY4SO2ZOv8SIrsWPGcvCYeqyxDCGY3PyKm8v5UlxwNZo60M7QVU9a9ivYDTZVji6xg9LBWvXBbU39xIgQtRQohkDwuFh7/Q9zdIyaLdNMcI5WTAA0RxdW+NfCYwiL9+wxxtUwOBwtVU7SfbNhRgutxh+9iFckD0v3vj9hoal59duV3WnRiO1/lXReXyIjM/lAwvY0MRWvH7qWvJ4nkxxxsP7BdiktQ+rsHYWD2cbpUT7mV0eu8SDwQOCa4MQHlwNWoj7wANVmORI87KCbPiAt9QBwc5nxS/A86TIhvwKT5xNIBOL6HLOANjDNEdArRO86XsRa6JheewLdWS2GQx7kEqrQeAzG1g22XLnifhSEC8noiaxBBpJ4USpjjisbVGxuJy2YLYbGoKV6yM+WmZ9Wl60/eSS3TClmCmaOLv6dJlyBYilW9Cq8cBTKx4OuuSkiU23nXrbA5Rnpg+z56lncp66RGCfl93GWw1Lcf0sQII0UdijJwi/GLntJsUwRpqRzy+yvLLREe2mtfEWvZwdxN/niRcuQUBivHaKK3uHL/Bp3X8qSKYJLRYuq0+JRVoTzxR8v/KazW6zJUcLmZrcDSTqoGrzydArLe2OlzddUzpWtniXe507MbgOKpA9xDsheYGXkB/isEtClyUxusyFW6zPCLdUkJHS5yLZ51NmFRTPzktJrBlMFBiPvpgRlE3TvlMJGqIXJ5nqdORThqkAZ6Ky3dHXkKSlS6iZEgZA6hjK4Rf9Ym5yH01SQcJzynVsyECOig/jWqU0sT8GGg+WicKEcGgDLDZqnp/3OBIgBBpFEYLJuI6ouWukl1o8Vc9TLHsLb5FPpeKPf1raQWRw1bkbV5w6doV5Q13/PL37dSb51STyr5iEhyHgaqcVCCDQJ+LGTYrk9SQ8RDxhv0DxcM9eAH+A5IGEsSfkleQBJBH06eVy30PycIe6ZJY8zJKHWfIwSx5mycMseZglD7PkYZY8zJKHWfIwSx5mycMseZglD7PkYZY8zJKHWfIwSx5mycMseZglD7PkYZY8zJKHWfIwSx5mycMseXh4yQM5qRvQq0l48gkn3lXycMeeS7+/5AGFwSeA+LrfNfl6ZQ0cenrZBPs9xA93LOM4ix9m8cMsfpjFD7P4YRY/zOKHWfwwix9m8cMsfpjFD7P4YRY/zOKHWfwwix9m8cMsfpjFD7P4YRY/zOKHWfwwix9m8cMsfpjFD7P4YRY/PLr4ASWAp9erPYA4/jSWzF9+rmfj31IFgf0dKgjiCf2xCgIkyevVNt5MAvF37JY4SyBmCcQsgZglELMEYpZAzBKIWQIxSyBmCcQsgZglELMEYpZAzBKIWQIxSyBmCcQsgZglELMEYpZAzBKIWQIxSyBmCcQsgZglELME4o+XQBBXs+/oE4q9q+qB+BtUDwQCP4Hgf1E9EMQT/o4bX5Cz8GEWPszCh1n4MAsfZuHDLHyYhQ+z8GEWPszCh1n4MAsfZuHDLHyYhQ+z8GEWPszCh1n4MAsfZuHDLHyYhQ+z8GEWPszCh1n4MAsfZuHD7y98IEjoaaL3r7QP8BP0rvteTJMZ/zQNP74jLutpAv7fqiCu1Q4u4TvBl2fMz9cIXCsmvINPBO476CJAHBkH/TUS+I01IF62KfkWhpdj/xMG4D9j8AdIIeipQ0Kln6UQ1TqMsmdtRPqpcZMrkHsx63irJhwxTbG5NetmkWoLmaEfYrtXWXms0UCROeBbnh+PCwedR3d8TS1SMyqWKWOpFrTUUsbgU3XHr0NouWYWmsqMVUtklDIJItGZwE4nvMalofGlkCB4WkyGKcGikJzB3lgmUevIPxCUjYb8Nj6OlcEzr03F5zZ/JU+kGg5yTrC7U0Jz7kpFljGSHbgYpgg2q029IjuIPnnugVmUTaLq/gpJFkRdcf3GxSEi2OLBtt67ksrxfVfVlL/vojWI+5gsgwoNi7m6p/BUsM5cArUeBa6sjQ2lYPHcnJTykGYgXW5OTT5Wy+u94FoeepmCr7Ys7O3Aar4xhk/Qo2OgqsqtxeDDCicotlszirUHzzSJuzJweb4g7VLrUXlOUyehTwxwZOVTn00sycHMtFURPy8vghU1ub0pEaQZ6zEDrwCibSpzSord+kRc6CWSLcXp3PCRwA3dfprR8KfpNGpA4X2lYuvdoqDGMVpK4yBgjmYiVrn3yKVUMiApXYYTejAj6niW9E1MH2WWKQ7bSGGy5U7m28tSi5ZMavktb7X9hgSIRDgFU+ZrAck0FoM7SAKP1rYApNY2WnNZv5V4t277nWaus5Aciw1aHHlkKJ9phwXrY6EvUmq18k9gNHVABY4JT4ka4frJGxhfpOyl5Ub8VGYYbr5Lt8PJAHpAo4PLGT2uIIvd5gyYeVTVUO103ax0VPINuqlpQLdaf2iE1DUDZVhCGw7GscuYwW15WJ+YtqKfixEhMicaK26PNBFu4z2HWVtuPZYaiSS3CMi0aWgtAxm1DgM3aJq0G5aN3Vw0nqCkFo3VSGBaJnWWGhUKBGZEWLXnOjbeA7oY6pIoefJQa8TGCg5gJxy5WhsHiBt5mmpyXIPg21YMd+7ULwtchBsxO7dnUVUoXWwXiziCJN3T6y5aXtLK0SWahhvScpVmDYxmet6d5G7Z9/uMRBxGtOouPJewD/FgexH7IXDbDXrxjjDNbEFXGevWLsSPiL9aeN06AEBzJYzezydFZu9yaHeGJA44HqFzQbqTx7eS6iV0aTCIFXMVmG+NEyef8M3iXJ1bs7TcpDljIuEPskIDm3OpqmXjkdvWaYlLW6xcLSedINhJQsCm+WILetu2OGLmYpqePB7GF+bYyW7sskG0xbmccr9YFha1UIAoEfpYgWBQXXinI5eqeuzXQ4mV1q7aN4QQgUI6yNuBM5WISYdhHdZpsqXXKrt1Bn/zXAWHdjLQi4M0BS+2kab+QM63Jb0YqVk1FAcelsSxyDGi3mpDeiyygmhjaBtl4krcevrs1UR0c8yX9aSJVqFee3npjtWyBxF8tuHrDkx2qJGDKZSxqz3rUmJRZy3InpupqGu4izVEHJ/kHthx/FTkiwdDV4yGbgZiCziQJAOQsmpsc2140QZGY2gduKtwy3djfHdHurZH1okkDiCNKoEhrdtYQeEb5OiXZF2SeCJg4MsPcpWAyasEDAFPyHUKRoBfkIKhf07BUyIt/5GQXKfVY+x505P/YtggwC3yfEy4B+fl24F/IjHAay4JAlcDSIJPCAZcDfq3o0nCv2A04d+CVH6D2WtMr6H6YiH/bOKfIcBf1nb7jkI+3SSRT7+CRt6xhd0fQCPZiT5S6icaSfE7kN1MM4Pl7oVGgu7GrE+6qkNGq2uHA8caF8+ECmPB8E0pKIwp7YA6rPVlZW13/HbbsLFqcQtVqHuqsFRZ4MGE2VZjCBX4mGKYNKLUU2E4eu64/BkbqqCe2sMDUeLIOotherNZ1RNhrbo0wMVwmh3u5X1+bgb0kCqaH5Rc4EAJRu9X+jTHhwtA5iWos6VjLRJhkm+jVZosA1HtJj5RI5oPi1L7PLOWWkvdIhx1KvyJtPaMtYXIu1XT0f3AeF6BW/0+nChbSi2zLJrmsRecTnhbdbM/rE2+7VZU7/eouJ7yDN2uaJprzud+R6g+6pO7kS52Pixfzv3IDKYTbzc9H2dW4BE+qDBic6iK/TaO9EijBLPHjUY5A0xt+rlvQCmRxnkFwXofbBj53DRbTO8PsuM05yQ7mKKxjRtt6k1I0/zeBsdqo4WHbi1Klj01iiV+d9KHmNheRDKQ6trsOzs8lhs5igdDJDfMsYab5RpcmsSl6uzytMljFmim9g2d41MvQYw92NkgE12bJgZIOysNNGkSUZcYc5dnrh2eTExJS6uGOOOkFxsacsBmN3Qr9FMrcbdoozEGyVG0LMNLkseMWRzapNwIizXXrPcAsT3ARqFa5ak0WcseL+QkBHsQlVDQ46xpAjjllyMxJWk/k2VRcqxmPGdWZq0+W/BqYnZe4ZmlmKynCYvczI0yHh/Y6cI8LWGJQbI62NhttRpHWcXsTikE2SkBvtjZmIciibFVsUHTF4uGX0Kj6e4n62PP+QFjjPUuyhksjNijJAFGsvZ8gWd4MjjwdghStIiyyX7pSXTl8gjr6tA53OxjnV82Ki15Jt91VZkvjjFlHleuZwpChwhFZAuixOxrknQ3noD5oQabizVT84LG1qRE1NLuMB2XNU8G1/wi6BeUzmOsXQHyxLQQ3qhrMBCns2yfG7Zhc3Z8qICU0x46m2zc94YiDdu1iootpBRFUAQuxdfPI0n1sSZNurZ+pbbbnBe2k92G8nhE3iLAot4N3I6EyYDX+mmmNw8/jQbOX7ZQn5HBPk2aBp3oZFRpmNk4vWCaLAzB00cEyxWmdJlSLRP2xG9Q2qpTqkT9ot8NtEvXjBXCober9YTtGuqiQqQEpeywdOHT8gI57jS3slyfjOi0NtSjVJ5Y6yAp/caiUacVn681XwsQmZSlK+ccJyGKaTE5NgDS2R8ob/DhxRkMkQ3tjq5WKWK+n4zZjyQiyXhjOuushDar6ZpNMFIqWUUCzu4JchxBwmgt3HCXoF3am2VtTpdfGVWyy09jPGl2STflCFXFeV7R6oy09u4WuOx1eGrLmlDv+SK7pFuqySDGREoJ3Wbq4hwvhuzEZ88S9XZVwoCzEo7wgrt04AGR1WyDOCuD2Q9GrzO0IzLcxccXaSoXn7Q3VACsMoy9tLCJDRRk5r7HERXHrBc79sAXwZFEdEEH6KG1tD1iTUKSlfTcCzWbTAl1ArGK6jmGoeoatlWGt1WDXEOpy1lIOr2XWfJKyJKVQzqOK0wC9KJ3hrQQNmzYqmfwdI790fep0k99bS90Ry1D1hdJ+xTvxpcLnpXQDIBBcLjKSibuckjFT6edJ8iqCQ5xbWtjgRHB031ArCo0Z4nXFr2zWqBs5HDoftIKKOu9uWLBtRot44TLAYNixfUlMxtjPey2ziFasb3ODYjt1M4JoNecOjKNmK3FGsLGzHxxsj0s7pMx0hXeNljqKHbxmdFhtzGXrns2dpTELTJ9pSj26MWtVxbTRBeXImmoMXVRwTTR5UssMrmaTsdyq1BTZAUhebbKTpzuoC4SbZslJ9eHU1ybx2PxLNHXNn2be6l7XKzFUxZNNd9aA0/mMsPk2m6FdV6RYdcdzwBfBx3d7LtC4tVa+Tx5ap7GLzQJR4tUvhI92xku5qIpQiMe4krZnYI2kP3m0ipreedP7gVDSrloPH7RZDHtbapDWi1KLm0oJQdkVOXzTqxHfAG5t7SipdaQx8c6dtg5sWRr6MjBxrFZoHLc5BAxXvn22BHlQfFzYpJlUHbM1TuOUyL4eAEsUfSQZeNGRfL/azezZUV1KAy/kcVgQC6VUUBBZrw5xSRsQQJh1KdvsrW73d1dp6rr1KnihkASCCsr6/tXwGvmCIu44/b7yej9tvB7XuK62CYf42JjeCxG4noA1fAxoZ61a+RY3Ijuza2tsnahogZ75gcmDsnlwTwJFBGrhA0eR6Um8+bod0WbXjTJjUr7pMu+ecaIF60dIoUbMpoDH2/zJrHbUm9wXiMOGIubUaebvXh5ZlI5b19pLH2Q9BAb8AXd+1G0GwX3GCu+yDLAl2FDBkNNb3TYNCmFgNUwdKhMdDoLeKNHGITS5aY6LA+MbmNanWYRHzz+XYUpdhewPdWJbFVaUBo4UZ519qDKMgKMR3vlzjSu5dmhg1Ru7bqIVP8o6fYcQVbonSQXt52+zLn+fNc7o+Oteu0cYw1m5sz30W3tt6BHHDk8LBfqZ532MtfebbAvvJt7eX1rwYLX2Blpy3ljS3gjwn0xF4Jwb/Nxqq5aVa6LODAdoQvOenwReSoxemNrqvKJJvDCXYcEzVdjFWoUjkJ8FmszQR9C8sCeh3Ld5XpXj3uSZ8jclpITTyjc5OQtluPUMgpzBRoZ1e/sWIlaRtJu+x3DZwGnhlcG5hJU08T0UsV1y26r0EJCQ91OTzZ2mFu2y3RqPAU7UVAJ46F781bfLLFCYte06YyGbAPTFTe9jh79hA5HNlGKAS9KYHzMgn6nDBRy3VHhL8qVDwJ2ktalVGTqcGgOQu1zW+E+ZSKgl4o+zaIldlKlpG8HKzY4LXbI9e6UwnGK+EniZV8KKmbEsw8na7y6HO/1pvPqWhPBuZ5POBjeqZYLRFSqeZ7jYBwf/w1ZfvyNuQLgjQ9/18BpavVdGX8nGPrvUxFulyEjvmIcoIhPzHhWNS1DcHlnbxyfTVYfdfm8UvR9s7T/SgEnFRzSVZZGS93Vx/IcW6wm4MQjzmt5H07sefXnFmAE0yF5go0EUb5Pf2YOcONOhPLs9R7/xFW0dPd57QdXgbcxezHXG1ItQ9t/JdavgFrDOvuFRF9F0QuYkuWLZejflIM/IfInj2GyeoLu/5wlYQD1VV+gaJZdUb8TLkn+QVSg/l6iwa74p4V8L0IQ9m+3y8tQFAeYYtsRvwE=</diagram></mxfile>", "zoom": "100%"}]}, "format": "short", "graphId": "flowchart_3", "gridPos": {"h": 18, "w": 24, "x": 0, "y": 5}, "id": 3, "links": [], "newFlag": false, "pluginVersion": "7.5.7", "rulesData": {"rulesData": [{"aggregation": "current", "alias": "AQS-1-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 1, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-1-temperature", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-290", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-290", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-2-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 2, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-2-temperature", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-310", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-310", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-3-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 3, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-3-temperture", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-328", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-328", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-4-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 4, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-4-temperature", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-281", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-281", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-5-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 5, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-5-temperature", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-270", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-270", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-6-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 6, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-6-temperature", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "20", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "20", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-7-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 7, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-7-temperature", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-259", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-259", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-8-temperature", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 8, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-8-temperature", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-237", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-237", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [27, 30], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Temperature", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "AQS-1-humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 9, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-1-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-294", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-294", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "AQS-2-humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 10, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-2-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-314", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-314", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "AQS-3humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 11, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-3-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-325", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-325", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "AQS-4-humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 12, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-4-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-285", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-285", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "AQS-5-humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 13, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-5-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-274", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-274", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "AQS-6-humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 14, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-6-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "23", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "23", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "AQS-7-humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 15, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-7-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-263", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-263", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "AQS-8-humidity", "colors": ["#73BF69", "#FF9830", "#F2495C"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 16, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "AQS-8-humidity", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-244", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-244", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [50, 65], "tooltip": true, "tooltipColors": true, "tooltipLabel": "<PERSON><PERSON><PERSON><PERSON>", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "humidity", "valueData": []}, {"aggregation": "current", "alias": "Window bathroom", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 17, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Window bathroom", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-306", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-307", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Opening", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Window  meeting room", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 18, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Window meeting room", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-233", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-236", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Opening", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Movement-entrance", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 19, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Movement entrance", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-297", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-298", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Occupancy", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Movement-production", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 20, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Movement-production", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-277", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-278", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Occupancy", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Movement-meeting-room", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 21, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Movement meeting-room", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-230", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-224", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Occupancy", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Movement-laboratory", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 22, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Movement laboratory", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-266", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-267", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Occupancy", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Movement-bathroom", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 23, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Movement bathroom", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-300", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-301", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Occupancy", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Movement-administration", "colors": ["rgb(198, 205, 209)", "#C0D8FF"], "column": "count", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": true, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 24, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Movement administration", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-253", "style": "fillColor"}, {"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-254", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [0], "tooltip": true, "tooltipColors": true, "tooltipLabel": "Occupancy", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Air-quality entrance", "colors": ["rgba(50, 172, 45, 0.97)", "#F2CC0C", "#f16330e4", "rgba(245, 54, 54, 0.9)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 25, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Air - quality entrance", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-288", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/", "/.*/"], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [1000, 3000, 10000], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Air-quality production", "colors": ["rgba(50, 172, 45, 0.97)", "#F2CC0C", "#f16330e4", "rgba(245, 54, 54, 0.9)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 26, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Air quality production", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-279", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/", "/.*/"], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [1000, 3000, 10000], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Air-quality laboratory", "colors": ["rgba(50, 172, 45, 0.97)", "#F2CC0C", "#f16330e4", "rgba(245, 54, 54, 0.9)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 27, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Air - quality laboratory", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-268", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/", "/.*/"], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [1000, 3000, 10000], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Air-quality meeting room", "colors": ["rgba(50, 172, 45, 0.97)", "#F2CC0C", "#f16330e4", "rgba(245, 54, 54, 0.9)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 28, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Air quality meeting room", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-232", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/", "/.*/"], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [1000, 3000, 10000], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Air-quality server room", "colors": ["rgba(50, 172, 45, 0.97)", "#F2CC0C", "#f16330e4", "rgba(245, 54, 54, 0.9)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 29, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Air quality server room", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-308", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/", "/.*/"], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [1000, 3000, 10000], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Air-quality administration", "colors": ["rgba(50, 172, 45, 0.97)", "#F2CC0C", "#f16330e4", "rgba(245, 54, 54, 0.9)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": true, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 30, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Air quality administration", "sanitize": false, "shapeData": [{"colorOn": "a", "hidden": false, "pattern": "dlWGD_ottbEwFKESu2L1-255", "style": "fillColor"}], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": ["/.*/", "/.*/"], "textData": [], "textProp": "id", "textRegEx": true, "thresholds": [1000, 3000, 10000], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "none", "valueData": []}, {"aggregation": "current", "alias": "Thermostat production", "colors": ["rgba(50, 172, 45, 0.97)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 31, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Thermostat production", "sanitize": false, "shapeData": [], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "3", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "Thermostat laboratory", "colors": ["rgba(50, 172, 45, 0.97)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 32, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Thermostat laboratory", "sanitize": false, "shapeData": [], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "18", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "Thermostat meeting room", "colors": ["rgba(50, 172, 45, 0.97)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 33, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Thermostat meeting room", "sanitize": false, "shapeData": [], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "14", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "Thermostat server room", "colors": ["rgba(50, 172, 45, 0.97)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 34, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Thermostat server room", "sanitize": false, "shapeData": [], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "12", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}, {"aggregation": "current", "alias": "Thermostat administration", "colors": ["rgba(50, 172, 45, 0.97)"], "column": "value", "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "eventData": [], "eventProp": "id", "eventRegEx": false, "gradient": false, "hidden": false, "invert": false, "linkData": [], "linkProp": "id", "linkRegEx": true, "mappingType": 1, "metricType": "table", "order": 35, "overlayIcon": false, "pattern": "/.*/", "rangeData": [], "reduce": true, "refId": "Thermostat administration", "sanitize": false, "shapeData": [], "shapeProp": "id", "shapeRegEx": true, "stringThresholds": [], "textData": [{"hidden": false, "pattern": "16", "textOn": "wmd", "textPattern": "/.*/", "textReplace": "content"}], "textProp": "id", "textRegEx": true, "thresholds": [], "tooltip": false, "tooltipColors": false, "tooltipLabel": "", "tooltipOn": "a", "tpDirection": "v", "tpGraph": false, "tpGraphHigh": null, "tpGraphLow": null, "tpGraphScale": "linear", "tpGraphSize": "100%", "tpGraphType": "line", "type": "number", "unit": "celsius", "valueData": []}]}, "targets": [{"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-1' and parameter = 'temperatura'", "refId": "AQS-1-temperature", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-1' and parameter = 'umidita'\n", "refId": "AQS-1-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-2' and parameter = 'temperatura'", "refId": "AQS-2-temperature", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-2' and parameter = 'umidita'\n", "refId": "AQS-2-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-3' and parameter = 'temperatura'", "refId": "AQS-3-temperture", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-3' and parameter = 'umidita'\n", "refId": "AQS-3-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-4' and parameter = 'temperatura'", "refId": "AQS-4-temperature", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-4' and parameter = 'umidita'\n", "refId": "AQS-4-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-5' and parameter = 'temperatura'", "refId": "AQS-5-temperature", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-5' and parameter = 'umidita'\n", "refId": "AQS-5-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-6' and parameter = 'temperatura'", "refId": "AQS-6-temperature", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-6' and parameter = 'umidita'\n", "refId": "AQS-6-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-7' and parameter = 'temperatura'", "refId": "AQS-7-temperature", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-7' and parameter = 'umidita'\n", "refId": "AQS-7-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-8' and parameter = 'temperatura'", "refId": "AQS-8-temperature", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-8' and parameter = 'umidita'\n", "refId": "AQS-8-humidity", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where (endpoint = 'MOS-3' or endpoint = 'MOS-4') and ((parameter = 'occupancy' and value = 'true') or (parameter = 'movimento' and value = 'true'));", "refId": "Movement-production", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where endpoint = 'MOS-1' and ((parameter = 'occupancy' and value = 'true') or (parameter = 'movimento' and value = 'true'));", "refId": "Movement entrance", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where endpoint = 'MOS-7' and ((parameter = 'occupancy' and value = 'true') or (parameter = 'movimento' and value = 'true'));", "refId": "Movement administration", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where endpoint = 'MOS-8' and ((parameter = 'occupancy' and value = 'true') or (parameter = 'movimento' and value = 'true'));", "refId": "Movement meeting-room", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where (endpoint = 'MOS-5' or endpoint = 'MOS-6') and ((parameter = 'occupancy' and value = 'true') or (parameter = 'movimento' and value = 'true'));", "refId": "Movement laboratory", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select avg(value::real) as value from allparameterstates_last where (endpoint = 'AQS-5' or endpoint = 'AQS-6') and parameter = 'voc';", "refId": "Air - quality laboratory", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-1' and parameter = 'voc';", "refId": "Air - quality entrance", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select avg(value::real) as value from allparameterstates_last where (endpoint = 'AQS-3' or endpoint = 'AQS-4') and parameter = 'voc';", "refId": "Air quality production", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-8' and parameter = 'voc';", "refId": "Air quality meeting room", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-7' and parameter = 'voc';", "refId": "Air quality administration", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'AQS-2' and parameter = 'voc';", "refId": "Air quality server room", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where endpoint = 'WIN-8' and parameter = 'opening' and value = 'true';\n", "refId": "Window meeting room", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where endpoint = 'Men Window' and parameter = 'opening' and value = 'true';\n", "refId": "Window bathroom", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select count(*) as count from allparameterstates_last where endpoint = 'Occupancy Sensor Bathroom' and parameter = 'occupancy' and value = 'true';\n", "refId": "Movement bathroom", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'TMST-3' and parameter = 'setpoint';\n", "refId": "Thermostat production", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'TMST-5' and parameter = 'setpoint';\n", "refId": "Thermostat laboratory", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'TMST-2' and parameter = 'setpoint';\n", "refId": "Thermostat server room", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'TMST-8' and parameter = 'setpoint';\n", "refId": "Thermostat meeting room", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}, {"format": "table", "group": [], "hide": false, "metricColumn": "none", "rawQuery": true, "rawSql": "select value::real from allparameterstates_last where endpoint = 'TMST-7' and parameter = 'setpoint';\n", "refId": "Thermostat administration", "select": [[{"params": ["value"], "type": "column"}]], "timeColumn": "time", "where": [{"name": "$__timeFilter", "params": [], "type": "macro"}]}], "timeFrom": null, "timeShift": null, "title": "EDALAB Office", "type": "agenty-flowcharting-panel", "valueName": "current", "version": "0.9.0"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "2021-08-17T04:49:24.254Z", "to": "2021-08-17T16:49:24.254Z"}, "timepicker": {}, "timezone": "", "title": "Flowcharting", "uid": "1o16kpnnk", "version": 54}