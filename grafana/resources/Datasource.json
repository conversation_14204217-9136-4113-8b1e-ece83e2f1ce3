{"id": 1, "uid": "z1ZJwYeGk", "orgId": 1, "name": "Remote_Server_Data", "type": "postgres", "typeLogoUrl": "", "access": "proxy", "url": "timescaledb", "password": "", "user": "userro", "database": "remote_server_data", "basicAuth": false, "basicAuthUser": "", "basicAuthPassword": "", "withCredentials": false, "isDefault": true, "jsonData": {"postgresVersion": 1100, "sslmode": "disable", "timescaledb": true, "tlsAuth": false, "tlsAuthWithCACert": false, "tlsConfigurationMethod": "file-path", "tlsSkipVerify": true}, "secureJsonData": {"password": "e7C3sM#XiNcx"}, "secureJsonFields": {"password": true}, "version": 6, "readOnly": false}