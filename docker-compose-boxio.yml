#
# <PERSON>: Hints for service comment to be used in services_checker.sh script
# service declaration must be prepended by a comment like
# # <service type>: <service name>
# <service type> can be oneshot (runs once and then stops) or persistent (typically a daemon)
# <sercice name> must match the container name as declared in the service
#
version: "3"

services:
  # persistent: mqtt
  mqtt:
    extends:
      file: docker-compose-common.yml
      service: mqtt

  # persistent: rabbitmq
  rabbitmq:
    extends:
      file: docker-compose-common.yml
      service: rabbitmq

  # persistent: auth-database
  auth-database:
    extends:
      file: docker-compose-common.yml
      service: auth-database

  # oneshot: auth-update
  auth-update:
    depends_on:
      - auth-database
    extends:
      file: docker-compose-common.yml
      service: auth-update

  # persistent: boxio-manager
  boxio-manager:
    extends:
      file: docker-compose-common.yml
      service: boxio-manager
    depends_on:
      - auth-database
      - rabbitmq
      - mqtt

  # persistent: couchdb
  couchdb:
    extends:
      file: docker-compose-common.yml
      service: couchdb

  # oneshot: couch-update
  couch-update:
    extends:
      file: docker-compose-common.yml
      service: couch-update
    depends_on:
      - couchdb

  # persistent: configuration-manager
  configuration-manager:
    extends:
      file: docker-compose-common.yml
      service: configuration-manager
    depends_on:
      - couchdb
      - rabbitmq
      - boxio-manager

  # persistent: nimbus
  nimbus:
    extends:
      file: docker-compose-common.yml
      service: nimbus
    depends_on:
      - zookeeper

  # persistent: supervisor
  supervisor:
    extends:
      file: docker-compose-common.yml
      service: supervisor
    depends_on:
      - nimbus
      - zookeeper
      - redis

  zookeeper:
    extends:
      file: docker-compose-common.yml
      service: zookeeper

  # persistent: webeditor
  webeditor:
    extends:
      file: docker-compose-common.yml
      service: webeditor
    depends_on:
      - auth-database
      - configuration-manager
    networks:
      boxio:

  timescale-data-access:
    extends:
      file: docker-compose-common.yml
      service: timescale-data-access
    depends_on:
      - boxio-manager

  # persistent: event-manager
  event-manager:
    extends:
      file: docker-compose-common.yml
      service: event-manager
    depends_on:
      - auth-database
      - rabbitmq

    #persistent
  mongodb:
    extends:
      file: docker-compose-common.yml
      service: mongodb

  health-service:
    extends:
      file: docker-compose-common.yml
      service: health-service
    depends_on:
      - auth-database
      - mongodb
      - rabbitmq

  # persistent: disk-size
  disk-size:
    extends:
      file: docker-compose-common.yml
      service: disk-size

  assistant-service:
    extends:
      file: docker-compose-common.yml
      service: assistant-service
    depends_on:
      - mqtt

  call-service:
    extends:
      file: docker-compose-common.yml
      service: call-service
    depends_on:
      - auth-database
      - rabbitmq

  redis:
    extends:
      file: docker-compose-common.yml
      service: redis

volumes:
  mqtt_plugins_volume:
    driver: local
    driver_opts:
      type: none
      device: ${PWD}/emqx/opt/emqx/etc/plugins
      o: bind

networks:
  boxio:
