#
# <PERSON>: Hints for service comment to be used in services_checker.sh script
# service declaration must be prepended by a comment like
# # <service type>: <service name>
# <service type> can be oneshot (runs once and then stops) or persistent (typically a daemon)
# <sercice name> must match the container name as declared in the service
#
version: "3"

services:
  # persistent: mqtt
  mqtt:
    extends:
      file: docker-compose-common-prod.yml
      service: mqtt
    ports:
      - ${MQTT_HTTP_PORT}:${MQTT_HTTP_PORT}
      - ${MQTT_WSS_PORT}:${MQTT_WSS_PORT}
      - ${MQTT_WS_PORT}:${MQTT_WS_PORT}
      - ${MQTT_INTERFACE_PORT}:${MQTT_INTERFACE_PORT}

  # persistent: rabbitmq
  rabbitmq:
    extends:
      file: docker-compose-common-prod.yml
      service: rabbitmq
    ports:
      - 15671:15671
      - 5671:5671
      - 5672:5672
      - 15672:15672

  # persistent: auth-database
  auth-database:
    extends:
      file: docker-compose-common-prod.yml
      service: auth-database
    ports:
      - 5432:5432

  # oneshot: auth-update
  auth-update:
    extends:
      file: docker-compose-common-prod.yml
      service: auth-update

  # persistent: boxio-manager
  boxio-manager:
    extends:
      file: docker-compose-common-prod.yml
      service: boxio-manager

  # persistent: couchdb
  couchdb:
    extends:
      file: docker-compose-common-prod.yml
      service: couchdb
    ports:
      - 5984:5984
      - 6984:6984

  # oneshot: couch-update
  couch-update:
    extends:
      file: docker-compose-common-prod.yml
      service: couch-update

  # persistent: configuration-manager
  configuration-manager:
    extends:
      file: docker-compose-common-prod.yml
      service: configuration-manager

  # persistent: nimbus
  nimbus:
    extends:
      file: docker-compose-common-prod.yml
      service: nimbus

  # persistent: supervisor
  supervisor:
    extends:
      file: docker-compose-common-prod.yml
      service: supervisor

  zookeeper:
    extends:
      file: docker-compose-common-prod.yml
      service: zookeeper

  # persistent: webeditor
  webeditor:
    extends:
      file: docker-compose-common-prod.yml
      service: webeditor
    ports:
      - 443:443
      - 9997:9997

    #persistent
  mongodb:
    extends:
      file: docker-compose-common-prod.yml
      service: mongodb
    ports:
      - 27017:27017

  timescale-data-access:
    extends:
      file: docker-compose-common-prod.yml
      service: timescale-data-access

  # persistent: event-manager
  event-manager:
    extends:
      file: docker-compose-common-prod.yml
      service: event-manager

  health-service:
    extends:
      file: docker-compose-common-prod.yml
      service: health-service

  # persistent: disk-size
  disk-size:
    extends:
      file: docker-compose-common-prod.yml
      service: disk-size

  assistant-service:
    extends:
      file: docker-compose-common-prod.yml
      service: assistant-service

  call-service:
    extends:
      file: docker-compose-common-prod.yml
      service: call-service

  redis:
    extends:
      file: docker-compose-common-prod.yml
      service: redis
