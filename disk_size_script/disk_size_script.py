#!/usr/bin/env python3
# coding=UTF-8
# sudo apt-get install pip3
# sudo apt-get install libpq-dev
# sudo pip3 install psycopg2
# sudo pip3 install flask
# sudo pip3 install waitress
import traceback
import shutil
import flask
from flask import request
from waitress import serve

app = flask.Flask(__name__)


def main():
    serve(app, host="0.0.0.0", port=9056)


@app.route('/disk', methods=['GET'])
def diskSize():
    try:
        total,used,free = shutil.disk_usage("/")
        disk_size = {}
        disk_size['total'] = total
        disk_size['used'] = used
        disk_size['free'] = free
        return disk_size
    except:
        traceback.print_exc()
        print("Connection Failed")


if __name__ == "__main__":
    main()
