import os
import sys
import random
import string
import datetime
from cryptography import x509
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.serialization import pkcs12
from cryptography.x509.oid import NameOID
from cryptography.x509.extensions import SubjectAlternativeName
from cryptography.hazmat.primitives import asymmetric
from cryptography.hazmat.primitives.serialization.pkcs12 import serialize_key_and_certificates


def usage():
    print(
        "Usage -h <hostname> [-d <alertnative_dns>] [-p <public ip>] [-l <local ip>] [-a <keystore_alias>] [-o <output_path>]")
    print("Note:")
    print("- keystore_alias is server by default")
    print("- output directory is /tmp by default")
    sys.exit(1)


def generate_password(length=15):
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def generate_certificate(hostname, alternative_dns="", public_ip="", local_ip="", keystore_alias="server", output_path="./",keystore_pwd=''):
    parent_path = output_path
    cert_path = parent_path
    os.makedirs(cert_path, exist_ok=True)

    key_pwd = generate_password()

    privkey_name = "privkey.pem"
    cert_name = "cert.pem"
    fullchain_name = "fullchain.pem"
    keystore_name = "keystore.p12"
    pubkey_name = "pubkey.pem"

    # Generate private key
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=4096,
        backend=default_backend()
    )

    # Generate certificate signing request (CSR)
    subject = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "IT"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Italy"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Verona"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "edalab.it"),
        x509.NameAttribute(NameOID.COMMON_NAME, hostname)
    ])
    extensions = []
    if local_ip:
        extensions.append(x509.IPAddress(ipaddress.IPv4Address(local_ip)))
    if public_ip:
        extensions.append(x509.IPAddress(ipaddress.IPv4Address(public_ip)))
    if alternative_dns:
        extensions.append(x509.DNSName(alternative_dns))
    csr = x509.CertificateSigningRequestBuilder().subject_name(
        subject).add_extension(
        SubjectAlternativeName(extensions),
        critical=False
    ).sign(private_key, hashes.SHA256(), default_backend())

    # Generate self-signed certificate
    issuer = subject
    builder = x509.CertificateBuilder().subject_name(
        subject).issuer_name(issuer).public_key(
        csr.public_key()).serial_number(x509.random_serial_number()).not_valid_before(
        datetime.datetime.utcnow()).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=3650)).add_extension(
        SubjectAlternativeName(extensions),
        critical=False
    )
    certificate = builder.sign(private_key, hashes.SHA256(), default_backend())

    # Save private key
    with open(os.path.join(cert_path, privkey_name), "wb") as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))

    # Save certificate
    with open(os.path.join(cert_path, cert_name), "wb") as f:
        f.write(certificate.public_bytes(serialization.Encoding.PEM))

    # Generate fullchain (certificate + CA certificate)
    with open(os.path.join(cert_path, fullchain_name), "wb") as f:
        f.write(certificate.public_bytes(serialization.Encoding.PEM))
        f.write(certificate.public_bytes(serialization.Encoding.PEM))
    # Generate keystore
    p12 = pkcs12.serialize_key_and_certificates(
        keystore_alias.encode(),
        private_key,
        certificate,
        [certificate],
        encryption_algorithm=serialization.BestAvailableEncryption(
            keystore_pwd.encode())
    )
    with open(os.path.join(cert_path, keystore_name), "wb") as f:
        f.write(p12)
    # Generate public key
    with open(os.path.join(cert_path, pubkey_name), "wb") as f:
        f.write(certificate.public_key().public_bytes(
            serialization.Encoding.PEM, serialization.PublicFormat.SubjectPublicKeyInfo))

    # Save passwords to a file
    password_file = os.path.join(cert_path, "password.txt")
    with open(password_file, "w") as f:
        f.write(f"KEY_PWD=\"{key_pwd}\"\n")
        f.write(f"KEYSTORE_PWD=\"{keystore_pwd}\"\n")
    print("Certificate generation completed.")


if __name__ == "__main__":
    generate_certificate()
