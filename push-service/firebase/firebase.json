{"type": "service_account", "project_id": "boxio-example-63432", "private_key_id": "9880c0777b8b24b77f6c01f7f0dbc007bef79d5b", "private_key": "-----B<PERSON>IN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCsKxHLJXdPH6po\nGcSwdTCexV2/nXftW8u71ZY/olONd20RSrUpNMX+WkaTu1vcRotiCbq/CQFy633y\ntFljpAb/2JY09Xfi3BLEyOEgi83I8MCVGVHJaq+E5K876Jx35ybf8SGUovNfoNyo\nAQhAk1bIfuHTOK9v0IQeK2siclLEgrcBLI9ysCGIXd6GeCX5n2R4rly0BoaBEQbg\nMZMkNhvkNUYqJaZx7uwHVCjYOzemxmR2rgl7E6gdw/OgN17jeqiCX1bVMp1bGlUF\nPYeUp1MLZGST/jAS6D1f/un7OqCKqZwAPyBX+8bO2ZKTBQgQSMfKkxKkEe2X21oR\nfNH+2J4RAgMBAAECggEAAX0CcQ+Dn14seo76KP8eCrY/c8KF4M3Ka8XNcT3/dErF\nT3JDzs46rStexKp549CWw7eovMWR2baz5rfAjmClKP+nKa8dPVpFhldaNo4yh0o8\n46WwJ7GqVCZ2PuBpXO+t64GE0cvjb3n2mDRIO+dKKes7zK4uOmA42RVNnnt1cWbQ\nOVs+vqkQH3d6jq5SXvQObbdvokie7NHtKBaUnH1lJsiJXe8xjM+rCst41ziigEs4\njWV0l9oBmf1Qdy4g35gVjiEjqdXBJqJy2KHdJ5UpNzovep3GeCONx9CpXInqaX88\nc7kjfoRgeUY6CQOyCngfexfxbgM0V6/PJ7k4PuQ1cQKBgQDqUVs2tOM1UZcP/91P\nfR0vGQIGTbjfazzvTijfFLzJlnQYsc47RT/0ykxDhHtAXo3Z5NXj9O7x9PmrMPYz\n4uvi4QkqDdhsF9beD/ivetLc5GW8yorTQQG0iSffOSo3w/sSK9gf3mK6nVfeCP3r\nTOCWluqFqWEAuOQ8jL/ugCsq1QKBgQC8GXtOom2Avj0UqrRcvYX/mZLFMuswvYDj\nvPnSwW/j3P+SViDw4+dgWVXrsAdY6uoOM4FA++Eb74K3lDaK0a/dV/Wajyqu6h0m\nzVKirt93ioCUB3M4OQUfxRZCJJW7dH8mb/XDWbZsUhJ5wcxy7QszHrc50jA54GQw\n4cHfhE/MTQKBgEmQFyHaskZIAjQMsQ9GiUYDBOazuemygiPjFuPKX+at7G7gS43C\nFO0mIcFEs2knBkO1Gw2BFQ8zSLrdGT567HGDEtx4Ek0FQvjebMWyusaMkgAuWl0u\nWHm0YacsDDwLtCQlTanbqIVL5o0Xp5gav5unb6Nc5M5UPZtg2NTouiStAoGBAKt2\n2wWCYSmTwSA+v+0uQqEOAFHMqcIjbQOst1rcRD/FrB9cbejJB5i3tvAC5Z+zfKp3\nXuK6+Kg7e8AW1mVS4ep2jQdYbINIHEzLrAFqQYC5+5108R4WNEqSoUioCun3NxT1\nyGVMOVOiuP7On60/0g7GeCW4KYgIuawpj05OUWOdAoGBAIT7WR2fYtgeAM8Xbnsg\ny7kZm+IEoWJEdhGN4/C3XqGBnr0Nb+OzXEbjFLEF8EX+hNL8GU/AAKw9Vch+FXME\nq8izAqCe9EvJ1McvEo7sQU9boPAsfnBIlBap3oTggAB8vg+T2y1cqQETDxkgikAH\nDiyjGcEcNDUH9dH/zCWGFSaj\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "117514280847070005036", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-3ce7h%40boxio-example-63432.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}