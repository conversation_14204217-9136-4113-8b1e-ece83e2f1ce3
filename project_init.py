#!/usr/bin/env python3
# sudo apt install python3-pip
# pip3 install docker
# pip3 install cryptography
# pip3 install python-dotenv
# coding=UTF-8
import os
import random
import string
import pathlib
import subprocess
import sys
import threading
import re
import shutil
import errno
import gzip
import time
import docker
import stat
from dotenv import dotenv_values, set_key

from project_cert_gen import generate_certificate

FUNCTION_DELIMITER = "#############################################"
BOXIO_MANAGER_FOLDER_NAME = "boxio-manager"
CERTS_FOLDER_NAME = "certs"
CONF_MANAGER_FOLDER_NAME = "configuration-manager"
EMQX_FOLDER_NAME = "emqx"
ETC_FOLDER_NAME = "etc"
OPT_FOLDER_NAME = "opt/boxio"
SHARED_FOLDER_NAME = "shared"
STORM_FOLDER_NAME = "storm"
TIMESCALE_FOLDER_NAME = "timescale-data-access"
COUCHDB_FOLDER_NAME = "couchdb"
AUTH_DATABASE_FOLDER_NAME = "auth-database"
TIMESCALE_DATABASE_FOLDER_NAME = "timescaledb"
ICINGA_FOLDER_NAME = "icinga"
# This folder
DIRECTORY_FOLDER = pathlib.Path(__file__).parent.absolute()
# Services dirs
PROFILE_FILE = "/etc/profile.d/set-docker-profile.sh"
BOXIO_MANAGER_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, BOXIO_MANAGER_FOLDER_NAME)
CERTS_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, CERTS_FOLDER_NAME)
CONF_MANAGER_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, CONF_MANAGER_FOLDER_NAME)
EMQX_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, EMQX_FOLDER_NAME)
ETC_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, ETC_FOLDER_NAME)
OPT_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER)
SHARED_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, SHARED_FOLDER_NAME)
STORM_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, STORM_FOLDER_NAME)
TIMESCALE_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, TIMESCALE_FOLDER_NAME)
COUCHDB_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, COUCHDB_FOLDER_NAME)
AUTH_DATABASE_BACKUP_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, AUTH_DATABASE_FOLDER_NAME)
TIMESCALE_DATABASE_BACKUP_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, TIMESCALE_DATABASE_FOLDER_NAME)
DISK_SERVICE_FILE = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, "disk_size_script", "disk_size.service")
SYSTEMD_DIR = pathlib.Path("/lib/systemd/system/")
ICINGA_GIT_DIR = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, ICINGA_FOLDER_NAME)
ICINGA_PLUGIN_DEST_DIR = pathlib.Path("/usr/lib/nagios/plugins/")
COUCHDB_LOCAL_CONF = pathlib.Path.joinpath(
    COUCHDB_DIR, "etc", "local.d", "local.ini_example")
COUCHDB_DEFAULT_CONF = pathlib.Path.joinpath(
    COUCHDB_DIR, "etc", "local.d", "default.ini_example")
# Env files
ENV_FILE = pathlib.Path.joinpath(DIRECTORY_FOLDER, ".env")
ENV_FILE_EX = pathlib.Path.joinpath(DIRECTORY_FOLDER, ".env_example")
# Certs file
CERTS_FOLDER = os.path.join(os.path.dirname(
    os.path.realpath(sys.argv[0])), "certs")
AUTHDB_FOLDER = os.path.join(os.path.dirname(
    os.path.realpath(sys.argv[0])), "auth-database")
GENERATE_CERTS_FOLDER = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, "certs-generation")
GENERATE_CA_SCRIPT = pathlib.Path.joinpath(
    GENERATE_CERTS_FOLDER, "generateCA.sh")
GENERATE_CERTS_SCRIPT = pathlib.Path.joinpath(
    GENERATE_CERTS_FOLDER, "generateCerts.sh")
GENERATE_LETS_SCRIPT = pathlib.Path.joinpath(
    GENERATE_CERTS_FOLDER, "generateLets.sh")

# Configuration files
EMQX_AUTH_CONF = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, EMQX_FOLDER_NAME, "opt", "emqx", "etc", "plugins", "emqx_auth_pgsql.conf_example")

# Backup and restore
BACKUP_RESTORE_SCRIPT = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, "backup-restore", "boxio-backup-ovh.sh")
BACKUP_RESTORE_SCRIPT_DEST_DIR = pathlib.Path('/usr/local/sbin/')
BACKUP_RESTORE_CRONJOB = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, "backup-restore", "boxio-backup")
BACKUP_RESTORE_CONF = pathlib.Path.joinpath(
    DIRECTORY_FOLDER, "backup-restore", "boxio-backup.conf_example")
BACKUP_RESTORE_CRON_DEST_DIR = pathlib.Path(
    "/etc/cron.d/")

CONFIGURATIONS = [BACKUP_RESTORE_CONF, EMQX_AUTH_CONF]


def random_generation(n):
    '''
    Generate a random string of ascii and digits
    '''
    return ''.join(random.SystemRandom().choice
                   (string.ascii_uppercase + string.digits) for _ in range(n))


def copy_env_example():
    '''
    Copy the environment example files in order to initialize the settings for
    each component
    '''
    print("Creating environment file")
    subprocess.call(['groupadd', '-g', '990', 'ssl-cert'])
    if ENV_FILE.is_file():
        print("Environment file already exists")
        return
    with ENV_FILE.open("w", encoding="utf-8") as env:
        with ENV_FILE_EX.open("r", encoding="utf-8") as env_ex:
            env_ex_txt = env_ex.read()
            env.write(env_ex_txt)
    print("Done!")


def copy_couchdb_confs():
    print("Copy couchdb configuration files")

    couchdb_local_final_name = pathlib.Path(
        str(COUCHDB_LOCAL_CONF).replace("_example", ""))
    couchdb_default_final_name = pathlib.Path(
        str(COUCHDB_DEFAULT_CONF).replace("_example", ""))

    if couchdb_local_final_name.is_file():
        print("Couchdb local file already exists ", couchdb_local_final_name)
    else:
        shutil.copyfile(COUCHDB_LOCAL_CONF, couchdb_local_final_name)

    if couchdb_default_final_name.is_file():
        print("Couchdb default file already exists ", couchdb_default_final_name)
    else:
        shutil.copyfile(COUCHDB_DEFAULT_CONF, couchdb_default_final_name)
    print("Done!")


def generate_passwords(and_certs):
    '''
    Replace %s in env files with a random password
    '''
    print("Generating passwords")
    text = ""
    with ENV_FILE.open("r", encoding="utf-8") as text_file_r:
        old_text = text_file_r.read()
        if "%s" not in old_text:
            text = old_text
            print("Passwords had already been initialized")
        else:
            text = old_text
            while "%s" in text:
                text = text.replace("%s", random_generation(16), 1)
            print("Writing new passwords to file")
        if "%URL" in old_text:
            fqdn = input("Insert server FQDN e.g: google.com\n")
            text = text.replace("%URL", fqdn)
        if "%REGISTRY_NAME" in old_text:
            fqdn = input(
                "Insert registry name registry.edalab.it or registry-dev.edalab.it:5000\n")
            text = text.replace("%REGISTRY_NAME", fqdn)
        if "%SERVER_NAME" in old_text:
            server = input(
                "Insert server name to be written in email body e.g: Production edalab server\n")
            text = text.replace("%SERVER_NAME", server)

    with ENV_FILE.open("w", encoding="utf-8") as text_file:
        text_file.write(text)
    if (and_certs):
        env_variables = dotenv_values(".env")
        hostname = env_variables.get("SERVER_URL")
        keystore_pwd = env_variables.get("BM_JWT_KEYSTORE_PASSWORD")
        generate_certificate(
            hostname=hostname, output_path="/opt/boxio/boxio-manager/certs/", keystore_pwd=keystore_pwd)
        fix_env_file()
        generate_certs(old_text, text)
    print("Done!")


def fix_env_file():
    '''
    Put the public key for jwt inside the .env file and the configuration files
    '''
    # Read the values from the .env file
    env = dotenv_values('.env')
    # Read the public key from the file
    with open('boxio-manager/certs/pubkey.pem', 'r', encoding="utf-8") as key_file:
        public_key = key_file.read().replace("\r", "").replace("\n", "")
    print(public_key)
    # Update the value in the environment dictionary
    env['JWT_PUBLIC_KEY'] = public_key

    # Set the updated value in the .env file without altering placeholders
    with open('.env', 'r') as env_file:
        lines = env_file.readlines()

    with open('.env', 'w') as env_file:
        for line in lines:
            if line.startswith('JWT_PUBLIC_KEY='):
                env_file.write(f'JWT_PUBLIC_KEY={public_key}\n')
            else:
                env_file.write(line)

    print("Public key updated successfully!")


def generate_configurations():
    '''
    Generate configuration files and replace placeholder
    '''
    print("Generating configuration files")
    text = ""
    d = dict()
    with ENV_FILE.open("r", encoding="utf-8") as text_file_r:
        # old_text = text_file_r.read()
        for line in text_file_r:
            if "=" in line:
                elements = line.rstrip().split("=")
                d[elements[0]] = elements[1]
    for config_file in CONFIGURATIONS:
        config_file_final_name = pathlib.Path(
            str(config_file).replace("_example", ""))
        if config_file_final_name.is_file():
            print("Configuration file already exists ", config_file_final_name)
            continue
        lines = []
        with config_file.open("r", encoding="utf-8") as config_txt:
            for line in config_txt:
                if "%" in line:
                    new_txt = line.rstrip().split("%")
                    if new_txt[1] in d:
                        new_txt[1] = d[new_txt[1]]
                        line = "".join(new_txt)+"\n"
                lines.append(line)
        with config_file_final_name.open("w", encoding="utf-8") as file:
            file.writelines(lines)
    copy_couchdb_confs()
    print("Done!")


def ownership_certs():
    '''
    Give ownership over the certs directory to ssl-cert group, and create it
    TODO ADD group ssl-cert with id 990
    '''
    for root, dirs, files in os.walk(CERTS_FOLDER):
        for momo in dirs:
            print(momo)
            os.chown(os.path.join(root, momo), 0, 990)
        for momo in files:
            print(os.path.join(root, momo))
            os.chown(os.path.join(root, momo), 0, 990, follow_symlinks=False)
    for root, dirs, files in os.walk(AUTHDB_FOLDER):
        for momo in dirs:
            print(momo)
            os.chown(os.path.join(root, momo), 1000, 1000)
        for momo in files:
            print(os.path.join(root, momo))
            os.chown(os.path.join(root, momo), 1000,
                     1000, follow_symlinks=False)


def download_backup():
    '''
    Launch the script to copy backupped files onto the machine
    '''
    p = subprocess.Popen([BACKUP_RESTORE_SCRIPT, "-r"],
                         shell=False,
                         stderr=subprocess.PIPE,
                         stdin=subprocess.PIPE,
                         stdout=subprocess.PIPE)
    listOutput = []
    listDirectCopy = []
    listDirectory = []
    pattern = re.compile(r'(?<=Restoring files into )(.*)(?= directory)')
    final_exit = False

    def read_stdout():
        '''
        Read received informations
        '''
        exit = False
        started_files = False
        containing_folder = None
        while not exit:
            poll = p.poll()
            if poll is not None:
                exit = True
                print('Backup downloaded... Tap enter key to proceed')
                finalExit = True
            msg = p.stdout.readline()
            msg_decode = msg.decode()
            print(msg_decode)
            if not containing_folder:
                containing_folder = pattern.findall(msg_decode)
                if containing_folder:
                    containing_folder = containing_folder[0]
                    listDirectory.append(containing_folder)
            if not started_files:
                if 'receiving incremental file list' in msg_decode:
                    started_files = True
            # listOutput.append(msg.decode())

    def read_stderro():
        exit = False
        while not exit:
            poll = p.poll()
            if poll is not None:
                print('Finish err')
                exit = True
            msg = p.stderr.readline()
            print("stderr: ", msg.decode())
    threading.Thread(target=read_stdout).start()
    threading.Thread(target=read_stderro).start()
    while not final_exit:
        res = input(">")
        poll = p.poll()
        if poll is not None:
            final_exit = True
        else:
            p.stdin.write((res + '\n').encode())
            p.stdin.flush()
    print(listOutput)
    # Untar each archived file inside a folder
    unzip_directory = listDirectory[0]
    for file_name in os.listdir(unzip_directory):
        file_path = os.path.join(unzip_directory, file_name)
        print(file_name)
        shutil.unpack_archive(file_path, unzip_directory)
    for file_name in os.listdir(unzip_directory):
        file_path = os.path.join(unzip_directory, file_name)
        print(file_name)
        if file_name.endswith('.sql.gz'):
            listDirectCopy.append(file_path)
            print(f"Appended to sql_gz_files: {file_name}")
        else:
            shutil.unpack_archive(file_path, unzip_directory)

    # Individually manage each file
    manage_boxio_manager(unzip_directory)
    manage_certs(unzip_directory)
    manage_conf_manager(unzip_directory)
    manage_emqx(unzip_directory)
    manage_etc(unzip_directory)
    manage_opt(unzip_directory)
    manage_shared(unzip_directory)
    manage_storm(unzip_directory)
    manage_timescale(unzip_directory)
    manage_couchdb(unzip_directory)
    manage_databases(unzip_directory, listDirectCopy)


def restore_approval(component_name):
    '''
    Request approval for operation
    '''
    restore = 'z'
    while (restore != 'y' and restore != 'n'):
        restore = input(
            "Do you wish to restore %s? [y] or [n]" % (component_name))
    return restore == 'y'


def installation_approval(component_name):
    '''
    Request approval for operation
    '''
    restore = 'z'
    print(f'\033[93mRead the document https://edalab.slab.com/posts/icinga-h7s61i58#hep1o-istruzioni-per-installazione for satellite installation instructions.\033[0m')
    while (restore != 'y' and restore != 'n'):
        restore = input(
            "Do you wish to install %s? [y] or [n]" % (component_name))
    return restore == 'y'


def manage_properties(unzip_directory, component_name,
                      target_dir, remove=False):
    '''
    Unzip the specified component and copy it into the root directory,
    if needed overwrite existing ones
    '''
    unzip_path = pathlib.Path(unzip_directory)
    if (restore_approval(component_name)):
        file_src = pathlib.Path.joinpath(
            unzip_path, component_name)
        if os.path.exists(target_dir) and remove:
            shutil.rmtree(target_dir)
        shutil.copytree(file_src, target_dir, dirs_exist_ok=True)


def manage_boxio_manager(unzip_directory):
    '''
    Restore boxio-manager
    '''
    manage_properties(unzip_directory, BOXIO_MANAGER_FOLDER_NAME,
                      BOXIO_MANAGER_DIR)


def manage_certs(unzip_directory):
    '''
    Restore certs dir
    '''
    manage_properties(unzip_directory, CERTS_FOLDER_NAME,
                      CERTS_DIR, remove=True)


def manage_conf_manager(unzip_directory):
    '''
    Restore conf manager
    '''
    manage_properties(unzip_directory, CONF_MANAGER_FOLDER_NAME,
                      CONF_MANAGER_DIR)


def manage_emqx(unzip_directory):
    '''
    Restore emqx
    '''
    manage_properties(unzip_directory, EMQX_FOLDER_NAME,
                      EMQX_DIR)


def manage_etc(unzip_directory):
    '''
    Restore etc folder
    '''
    manage_properties(unzip_directory, ETC_FOLDER_NAME,
                      ETC_DIR)


def manage_opt(unzip_directory):
    '''
    Restore opt folder
    '''
    manage_properties(unzip_directory, OPT_FOLDER_NAME,
                      OPT_DIR)


def manage_shared(unzip_directory):
    '''
    Restore shared folder
    '''
    manage_properties(unzip_directory, SHARED_FOLDER_NAME,
                      SHARED_DIR)


def manage_storm(unzip_directory):
    '''
    Restore storm folder
    '''
    manage_properties(unzip_directory, STORM_FOLDER_NAME,
                      STORM_DIR)


def manage_timescale(unzip_directory):
    '''
    Restore time series db
    '''
    manage_properties(unzip_directory, TIMESCALE_FOLDER_NAME,
                      TIMESCALE_DIR)


def manage_couchdb(unzip_directory):
    '''
    Restore couchdb
    '''
    manage_properties(unzip_directory, COUCHDB_FOLDER_NAME,
                      COUCHDB_DIR)


def manage_databases(unzip_directory, list_direct_copy):
    '''
    Manage restore of databases
    '''
    for root, dirs, files in os.walk(AUTHDB_FOLDER):
        print(root, dirs, files)
        for momo in dirs:
            print(momo)
            os.chown(os.path.join(root, momo), 1000, 1000)

    for file_path in list_direct_copy:
        file_name = file_path.split('/')[-1]
        print(file_path)
        if file_name == 'remote_server_data.sql.gz' or file_name == 'remote_server.sql.gz':
            unzip_pathlib = pathlib.Path(unzip_directory)
            file_pathlib = pathlib.Path(file_path)
            file_out = pathlib.Path.joinpath(unzip_pathlib, file_name)
            with gzip.open(file_pathlib, 'rb') as f_in:
                with open(file_out, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            if file_name == 'remote_server.sql.gz':
                target_dir = AUTH_DATABASE_BACKUP_DIR
            if file_name == 'remote_server_data.sql.gz':
                target_dir = TIMESCALE_DATABASE_BACKUP_DIR
            os.makedirs(target_dir, exist_ok=True)
            shutil.copy(file_out,  pathlib.Path.joinpath(
                target_dir, file_name))
    for file_path in list_direct_copy:
        file_name = file_path.split('/')[-1]
        unzip_pathlib = pathlib.Path(unzip_directory)
        file_pathlib = pathlib.Path(file_path)
        file_out = pathlib.Path.joinpath(unzip_pathlib, file_name)
        if file_name == 'remote_server_data.sql.gz' or file_name == 'remote_server.sql.gz':
            manage_database('auth-database')


def manage_database(component_name):
    if restore_approval(component_name):
        client = docker.from_env()
        subprocess.call(['docker', 'compose', 'down'])
        subprocess.call(['docker', 'compose',
                         '-f', 'docker-compose-restore.yml',
                         'up',
                         '-d'])
        container = client.containers.get(component_name)
        time.sleep(8)
        container.exec_run(
            'psql -U postgres_user -c "DROP DATABASE remote_server"')
        container.exec_run(
            'psql -U postgres_user -c "CREATE DATABASE remote_server"')
        time.sleep(4)
        print("STEP 1")
        container.exec_run(
            'psql -U postgres_user -d remote_server -c "CREATE EXTENSION timescaledb"')
        print("STEP 2")
        container.exec_run(
            'psql -U postgres_user -d remote_server -c "SELECT timescaledb_pre_restore()"')
        print("STEP 3")
        cmd = (
            "/bin/bash -c 'psql -U postgres_user -d remote_server < /remote_server.sql.gz'")
        print("STEP 4")
        container.exec_run(cmd)
        container.exec_run(
            'psql -U postgres_user -d remote_server -c "SELECT timescaledb_post_restore()"')
        container.exec_run(
            'psql -U postgres_user -d remote_server -c "SELECT timescaledb_pre_restore()"')
        container.exec_run(cmd)
        container.exec_run(
            'psql -U postgres_user -d remote_server -c "SELECT timescaledb_post_restore()"')
        print("STEP 1")


def copy_disk_script():
    '''
    Setup the script for calculating disk space
    '''
    shutil.copy(DISK_SERVICE_FILE,  pathlib.Path.joinpath(SYSTEMD_DIR))
    subprocess.call(['systemctl', 'enable', 'disk_size'])


def backup_restore():
    '''
    Start the backup procedure
    '''
    download_backup()


def activate_backup(activate):
    '''
    Choose whether to activate backup or not
    '''
    if (not activate or installation_approval('backup-procedure')):
        shutil.copy(BACKUP_RESTORE_CRONJOB,  BACKUP_RESTORE_CRON_DEST_DIR)
        text = ""
        d = dict()
        with ENV_FILE.open("r", encoding="utf-8") as text_file_r:
            # old_text = text_file_r.read()
            for line in text_file_r:
                if "=" in line:
                    elements = line.rstrip().split("=")
                    d[elements[0]] = elements[1]
            config_file = BACKUP_RESTORE_CONF
            config_file_final_name = pathlib.Path(
                str(config_file).replace("_example", ""))
            if config_file_final_name.is_file():
                print("Configuration file already exists ",
                      config_file_final_name)
                return
            lines = []
            with config_file.open("r", encoding="utf-8") as config_txt:
                for line in config_txt:
                    if "%" in line:
                        new_txt = line.rstrip().split("%")
                        new_txt[1] = d[new_txt[1]]
                        line = "".join(new_txt)+"\n"
                    lines.append(line)
            with config_file_final_name.open("w", encoding="utf-8") as file:
                file.writelines(lines)


def setup_other_services():
    '''
    Install dependencies for disk space api script
    '''
    print("Installing more dependencies")
    subprocess.check_call(['apt', 'install', 'python3-pip'])
    subprocess.check_call(['pip3', 'install', 'flask'])
    subprocess.check_call(['pip3', 'install', 'waitress'])
    subprocess.check_call(['systemctl', 'restart', 'disk_size'])
    subprocess.check_call(
        ['ssh-keyscan', 'netmon.edalab.it', '>>', '~/.ssh/known_hosts'])


def install_docker():
    '''
    Install docker
    '''
    print("Installing more dependencies")
    subprocess.check_call(['sudo', 'apt-get', 'update'])
    subprocess.check_call(['sudo', 'apt-get', 'install',
                           'ca-certificates', 'curl', 'gnupg', 'lsb-release'])
    p1 = subprocess.Popen(
        ['curl', '-fsSL', 'https://download.docker.com/linux/ubuntu/gpg'], stdout=subprocess.PIPE)
    p2 = subprocess.Popen(['sudo', 'gpg', '--dearmor', '-o',
                           '/usr/share/keyrings/docker-archive-keyring.gpg'], stdin=p1.stdout, stdout=subprocess.PIPE)
    p2.communicate()[0]
    architecture = subprocess.check_output(
        ['dpkg', '--print-architecture']).decode('utf-8').rstrip('\n')
    release = subprocess.check_output(
        ['lsb_release', '-cs']).decode('utf-8').rstrip('\n')
    p3 = subprocess.Popen(
        ['echo', f'deb [arch={architecture} signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu {release} stable'], stdout=subprocess.PIPE)
    p4 = subprocess.Popen(['sudo', 'tee', '/etc/apt/sources.list.d/docker.list',
                           '>', '/dev/null'], stdin=p3.stdout, stdout=subprocess.PIPE)
    p4.communicate()[0]
    subprocess.check_call(['sudo', 'apt-get', 'update'])
    subprocess.check_call(['sudo', 'apt-get', 'install', 'docker-ce',
                           'docker-ce-cli', 'containerd.io', 'docker-compose-plugin'])
    subprocess.check_call(['apt', 'install', 'python3-pip'])
    subprocess.check_call(['pip3', 'install', 'docker'])
    subprocess.check_call(['mkdir', '-p', '/root/.docker/cli-plugins'])
    subprocess.check_call(['curl', '-SL', 'https://github.com/docker/compose/releases/download/v2.4.1/docker-compose-linux-x86_64',
                           '-o', '/root/.docker/cli-plugins/docker-compose'])
    subprocess.check_call(
        ['chmod', '+x', '/root/.docker/cli-plugins/docker-compose'])


def setup_icinga():
    '''
    Install icinga and copy monitoring scripts in the correct directory
    '''
    if (installation_approval('icinga')):
        print("Starting icinga2's installation")
        subprocess.check_call(['apt-get', 'update'])
        subprocess.check_call(
            ['apt-get', '-y', 'install', 'apt-transport-https', 'wget', 'gnupg'])
        subprocess.check_call(['apt-get', 'install', 'monitoring-plugins'])
        subprocess.check_call(['apt-get', 'install', 'icinga2'])
        subprocess.check_call(
            ['chown', '-R', 'nagios:nagios', '/etc/icinga2/'])
        subprocess.check_call(['icinga2', 'node', 'wizard'])
        copy_icinga_scripts()


def generate_certs(old_text, text):

    d = {}
    values = dotenv_values(".env")
    if (not restore_approval('certificates')):
        return
    option = input(
        "Choose certificate type: \n 1 - Self-signed \n 2 - Let's Encrypt \n By default option 1. \n Digit your choice: ")
    if (option == "2"):
        print("SELECTED LETS")
    if option == "2":
        # letsencrypt script
        if "%URL" in old_text:
            fqdn = input("LETSENCRYPT Insert server FQDN e.g: google.com: ")
            text = text.replace("%URL", fqdn)
            print(fqdn)
            d['HOST'] = fqdn
        rc = subprocess.call([GENERATE_LETS_SCRIPT])
    else:
        # ssl script
        if "%URL" in old_text:
            fqdn = input("Insert server FQDN e.g: google.com or IP: ")
            text = text.replace("%URL", fqdn)
            print(fqdn)
            d['HOST'] = fqdn
        rc = subprocess.call(GENERATE_CA_SCRIPT)
        hostname = values.get('HOST')
        rc = subprocess.call([GENERATE_CERTS_SCRIPT, "-h", hostname])
    if "%SERVER_NAME" in old_text:
        server = input(
            "Insert server name to be written in email body e.g: Production edalab server: ")
        text = text.replace("%SERVER_NAME", server)
    if "%AUTHDB_BOXIO_USER" in old_text:
        email = input(
            "Insert global admin user email: ")
        text = text.replace("%AUTHDB_BOXIO_USER", email)

    with ENV_FILE.open("w", encoding="utf-8") as text_file:
        text_file.write(text)

    print("Done!")


def init_enabled_profiles():
    with ENV_FILE.open("r", encoding="utf-8") as file:
        lines = file.readlines()

    compose_variables = []

    for i, line in enumerate(lines):
        if '=' in line:
            try:
                key, value = map(str.strip, line.split('='))
            except:
                continue
            if key.endswith('_ENABLED') and value.lower() == 'true':
                variable_name = key.rsplit('_ENABLED', 1)[0].lower()
                compose_variables.append(variable_name)
                print(compose_variables)

    if compose_variables:
        compose_variables_line = f'export COMPOSE_PROFILES="{",".join(compose_variables)}"\n'
        print(compose_variables_line)
        print(compose_variables)

        # Check if COMPOSE_VARIABLES line already exists and replace it
        create_profile_file(compose_variables_line)
    else:
        print('No variables to update.')


def create_profile_file(lines):
    try:
        with open(PROFILE_FILE, 'w') as file:
            file.write(lines)
        bashrc_path = "/root/.bashrc"
        with open(bashrc_path, 'a') as file:
            file.write(lines)
        print(f"Appended to {bashrc_path} successfully.")
    except Exception as e:
        print(f"An error occurred: {e}")


def copy_icinga_scripts():
    subprocess.call(['usermod', '-a', '-G', 'docker', 'nagios'])
    for root, dirs, files in os.walk(ICINGA_GIT_DIR):
        for f in files:
            file_path = os.path.join(root, f)
            dest_path = os.path.join(ICINGA_PLUGIN_DEST_DIR, f)
            
            # Set ownership of the file
            os.chown(file_path, 0, 119)
            
            # Copy the file to the destination directory
            shutil.copy(file_path, ICINGA_PLUGIN_DEST_DIR)
            
            # Give group execute permission to 'nagios'
            os.chmod(dest_path, os.stat(dest_path).st_mode | stat.S_IXGRP)
        
        for momo in dirs:
            dir_path = os.path.join(root, momo)
            dest_dir_path = os.path.join(ICINGA_PLUGIN_DEST_DIR, momo)
            
            print(dirs)
            print(momo)
            os.chown(dir_path, 0, 119)
            try:
                print(dir_path)
                shutil.copytree(dir_path, ICINGA_PLUGIN_DEST_DIR, dirs_exist_ok=True)
            except OSError as exc:  # python >2.5
                if exc.errno in (errno.ENOTDIR, errno.EINVAL):
                    shutil.copy(dir_path, ICINGA_PLUGIN_DEST_DIR)
                else:
                    raise

            # After copying the directory, you may also want to change its permissions
            os.chmod(dest_dir_path, os.stat(dest_dir_path).st_mode | stat.S_IXGRP)


def is_jitsi():
    is_jitsi = 'nothing'
    while is_jitsi != 'y' and is_jitsi != 'n':
        is_jitsi = input("""
Is jitsi enabled? (y,n):
\n""")
    if (is_jitsi == 'y'):
        os.symlink(
            '/opt/boxio/docker-compose-boxio-jitsi.yml', '/opt/boxio/docker-compose.yml')
        os.symlink(
            '/opt/boxio/nginx/default-jitsi.conf', '/opt/boxio/nginx/default.conf')

    else:
        os.symlink(
            '/opt/boxio/docker-compose-boxio.yml', '/opt/boxio/docker-compose.yml')
        os.symlink(
            '/opt/boxio/nginx/default-boxio.conf', '/opt/boxio/nginx/default.conf')


def config_json_type():
    config_json = 'nothing'
    while config_json != 's' and config_json != 'f':
        config_json = input("""
Standard or FIAM config.json? (s,f):
\n""")
    if (config_json == 's'):
        os.symlink(
            '/opt/boxio/shared/config/config.json', '/opt/boxio/shared/config/endpoint.json')
    else:
        os.symlink(
            '/opt/boxio/shared/config/fiam-config.json', '/opt/boxio/shared/config/endpoint.json')


def main():
    '''
    Script for initializing / restore a Boxio server
    '''
    k = 'nothing'
    while k != '1' and k != '2' and k != '3':
        k = input("""
Please choose the operation that you wish to start. Input the number:
1. Starts the installation of a new server
2. Download the backups for this machine
3. Init enabled services
\n""")
    if k == '1':
        # subprocess.call(['docker', 'compose', 'down'])
        install_docker()
        copy_env_example()
        print(FUNCTION_DELIMITER)
        generate_passwords(True)
        print(FUNCTION_DELIMITER)
        generate_configurations()
        print(FUNCTION_DELIMITER)
        ownership_certs()
        print(FUNCTION_DELIMITER)
        setup_icinga()
        print(FUNCTION_DELIMITER)
        activate_backup(True)
        print(FUNCTION_DELIMITER)
        copy_disk_script()
        is_jitsi()
        config_json_type()
    if k == '2':
        subprocess.call(['docker', 'compose', 'down'])
        copy_env_example()
        generate_passwords(False)
        activate_backup(False)
        backup_restore()
        ownership_certs()
        generate_passwords(False)
    if k =='3':
        setup_icinga()
        exit(0)
    init_enabled_profiles()
    setup_other_services()


if __name__ == "__main__":
    main()
