#!/bin/bash

source ./.env .

echo $KEYSTORE_PASSWORD
echo $URL

sudo apt-get -y install certbot
sudo certbot certonly -d "$URL"  --standalone

DIR_CERT="server"
mkdir -p "$SSL_HOST_VOLUME/$DIR_CERT"
rm $SSL_HOST_VOLUME/$DIR_CERT/*
cp -rL /etc/letsencrypt/live/$URL/* $SSL_HOST_VOLUME/$DIR_CERT/
chmod 640 $SSL_HOST_VOLUME/$DIR_CERT/privkey.pem
chown -R 990:ssl-cert "$SSL_HOST_VOLUME"

PRIVKEY_NAME="$SSL_HOST_VOLUME/$DIR_CERT/privkey.pem"
CERT_NAME="$SSL_HOST_VOLUME/$DIR_CERT/cert.pem"
KEYSTORE_NAME="$SSL_HOST_VOLUME/$DIR_CERT/keystore.p12"
FULLCHAIN_NAME="$SSL_HOST_VOLUME/$DIR_CERT/fullchain.pem"
CHAIN_NAME="$SSL_HOST_VOLUME/$DIR_CERT/chain.pem"

openssl pkcs12 -name "boxio" -inkey "$PRIVKEY_NAME" -in "$FULLCHAIN_NAME" -export -out "$KEYSTORE_NAME" -passin pass:$KEYSTORE_PASSWORD -passout pass:$KEYSTORE_PASSWORD
chmod 640 $SSL_HOST_VOLUME/$DIR_CERT/keystore.p12
echo "Enable mainteance"

sudo cp /opt/boxio/maintenance/certbot.service /lib/systemd/system/
sudo systemctl daemon-reload
