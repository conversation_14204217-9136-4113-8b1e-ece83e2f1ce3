#!/bin/bash

function usage
{
    echo "Usage -h <hostname> [-d <alertnative_dns>] [-p <public ip>] [-l <local ip>] "
    exit 1
}


if [ $# -eq 0 ]; then
    usage
fi

while getopts "h:d:p:l:" OPT; do
    case $OPT in
        p)
            PUBLIC_IP=${OPTARG}
            if ! [[ $PUBLIC_IP =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                echo "ip address not valid"
                exit 1
            fi
        ;;
        l)
            LOCAL_IP=${OPTARG}
            if ! [[ $LOCAL_IP =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                echo "ip address not valid"
                exit 1
            fi
        ;;
	    d)
 	        ALTERNATIVE_DNS=${OPTARG}
	    ;;
        h)
            HOST=${OPTARG}
        ;;
        ?)
            usage
        ;;
        *)
            usage
        ;;
    esac
done

if [ -z "$HOST" ]; then
    usage
fi

read -p "Enter Organization (default edalab.it):" ORGANIZATION
echo ""

if [ -z "$ORGANIZATION" ]; then
    ORGANIZATION="edalab.it"
fi

read -p "Enter Location (default Verona):" LOCATION
echo ""

if [ -z "$LOCATION" ]; then
    LOCATION="Verona"
fi

read -p "Enter State (default Italy):" STATE
echo ""

if [ -z "$STATE" ]; then
    STATE="Italy"
fi

read -p "Enter Country (default IT):" COUNTRY
echo ""

if [ -z "$COUNTRY" ]; then
    COUNTRY="IT"
elif [ ! ${#COUNTRY} -eq 2 ]; then
    echo "Please insert Country abbreviation e.g. IT"
    exit 1
fi
COUNTRY=$(echo ${COUNTRY^^})



PARENT_PATH="."
CERT_PATH="$PARENT_PATH/$HOST"
if [[ -d $CERT_PATH ]]
then
    rm -r $CERT_PATH/*
fi

mkdir -p $CERT_PATH

SCRIPT_NAME=$(basename "$0")
CA_DIRECTORY="./ca"

if [[ ! -d "$CA_DIRECTORY" ]]
then
    echo "$CA_DIRECTORY does not exists in the same path of $SCRIPT_NAME"
    exit 1
fi

source $CA_DIRECTORY/config .
source ./.env .
CA_PATH="$CA_DIRECTORY/$CA_PATH"
CA_KEY_FILE="$CA_DIRECTORY/$CA_KEY_FILE"

PRIVKEY_NAME="privkey.pem"
CERT_NAME="cert.pem"
KEYSTORE_NAME="keystore.p12"
FULLCHAIN_NAME="fullchain.pem"
CHAIN_NAME="chain.pem"


echo "Generate key"
openssl genrsa -passout pass:$KEYSTORE_PASSWORD -out $CERT_PATH/$PRIVKEY_NAME 4096

echo "Generate $HOST certificate"

SAN="[SAN]\nsubjectAltName=DNS:$HOST"
if [ ! -z "$LOCAL_IP" ]; then
    SAN2=",IP:$LOCAL_IP"
    SAN="$SAN$SAN2"
fi

if [ ! -z "$PUBLIC_IP" ]; then
    SAN2=",IP:$PUBLIC_IP"
    SAN="$SAN$SAN2"
fi

if [ ! -z "$ALTERNATIVE_DNS" ]; then
    SAN2=",DNS:$ALTERNATIVE_DNS"
    SAN="$SAN$SAN2"
fi

set -x
openssl req -new -sha256 -key $CERT_PATH/$PRIVKEY_NAME -subj "/C=$COUNTRY/ST=$STATE/L=$LOCATION/O=$ORGANIZATION/CN=$HOST" -reqexts SAN -config <(cat /etc/ssl/openssl.cnf <(printf "$SAN")) -out "$CERT_PATH/cert.csr" -passin pass:$KEYSTORE_PASSWORD
chmod 640 "$CERT_PATH/$PRIVKEY_NAME"

echo "Generate signed certificate with the certificate authority"
openssl x509 -req -in "$CERT_PATH/cert.csr" -CA "$CA_PATH" -CAkey "$CA_KEY_FILE" -CAcreateserial -extensions SAN -extfile <(cat /etc/ssl/openssl.cnf <(printf "$SAN")) -out "$CERT_PATH/$CERT_NAME" -days 3650 -sha256 -passin pass:$CA_KEY 2>/dev/null

echo "Generate chain"
cat $CERT_PATH/cert.pem $CA_PATH > "$CERT_PATH/$FULLCHAIN_NAME"
chmod 640 "$CERT_PATH/$FULLCHAIN_NAME"

echo "Generate keystore"
openssl pkcs12 -name "boxio" -inkey "$CERT_PATH/$PRIVKEY_NAME" -in "$CERT_PATH/$FULLCHAIN_NAME" -export -out "$CERT_PATH/$KEYSTORE_NAME" -passin pass:$KEYSTORE_PASSWORD -passout pass:$KEYSTORE_PASSWORD
chmod 640 "$CERT_PATH/$KEYSTORE_NAME"

cp $CA_PATH "$CERT_PATH/$CHAIN_NAME"

echo "Generate publickey"
openssl x509 -in $CERT_PATH/$CERT_NAME -pubkey -noout > $CERT_PATH/certificate_publickey.pem

DIR_CERT="server"
mkdir -p "$SSL_HOST_VOLUME/$DIR_CERT"
mv $CERT_PATH/* "$SSL_HOST_VOLUME/$DIR_CERT"
rm -r "$CERT_PATH"
chown -R 990:ssl-cert "./certs"
