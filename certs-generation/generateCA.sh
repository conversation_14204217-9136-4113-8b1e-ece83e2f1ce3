#!/bin/bash

PWGEN=$(dpkg -l pwgen | grep ii | cut -d " " -f 1)
if [ "$PWGEN" != "ii" ]; then
	apt-get install -y pwgen
fi

if [ ! $(getent group ssl-cert) ]; then
	groupadd -g 990 ssl-cert
fi

read -p "Enter CA name:" NAME
echo ""

if [ -z "$NAME" ]; then
    echo "Please insert CA name"
    exit 1
fi

read -p "Enter Organization (default edalab.it):" ORGANIZATION
echo ""

if [ -z "$ORGANIZATION" ]; then
    ORGANIZATION="edalab.it"
fi

read -p "Enter Location (default Verona):" LOCATION
echo ""

if [ -z "$LOCATION" ]; then
    LOCATION="Verona"
fi

read -p "Enter State (default Italy):" STATE
echo ""

if [ -z "$STATE" ]; then
    STATE="Italy"
fi

read -p "Enter Country (default IT):" COUNTRY
echo ""

if [ -z "$COUNTRY" ]; then
    COUNTRY="IT"
elif [ ! ${#COUNTRY} -eq 2 ]; then 
    echo "Please insert Country abbreviation e.g. IT"
    exit 1
fi
COUNTRY=$(echo ${COUNTRY^^})

CA_PATH="./ca"

CERTS_PATH="$CA_PATH/certs"
CA_FILE="$CERTS_PATH/$NAME.pem"
CONFIG_CA_FILE="./certs/$NAME.pem"

PRIVATE_PATH="$CA_PATH/private"
CA_KEY_FILE="$PRIVATE_PATH/$NAME.key.pem"
CONFIG_CA_KEY_FILE="./private/$NAME.key.pem"

SERIAL_PATH="$CA_PATH/serial"

mkdir -p $CA_PATH

CONFIG_FILE="$CA_PATH/config"
:>$CONFIG_FILE

export K_PWD="$(pwgen -cn $PASSWORD_LENGTH 15)"
echo "CA_KEY=\""$(eval echo -n \"\$K_PWD\")"\"" >> $CONFIG_FILE
echo "CA_PATH=\""$(eval echo -n \"\$CONFIG_CA_FILE\")"\"" >> $CONFIG_FILE
echo "CA_KEY_FILE=\""$(eval echo -n \"\$CONFIG_CA_KEY_FILE\")"\"" >> $CONFIG_FILE

mkdir -p $CERTS_PATH
#mkdir -p $CA_PATH/crl 
#mkdir -p $CA_PATH/newcerts
mkdir -p $PRIVATE_PATH

chmod 700 $CA_PATH/private
#touch $CA_PATH/index.txt
echo 1000 > $SERIAL_PATH

echo "Generate Key"
openssl genrsa -aes256 -passout pass:$K_PWD -out "$CA_KEY_FILE" 4096
chmod 400 "$CA_KEY_FILE"

echo "Generate Certs"
openssl req -key "$CA_KEY_FILE" -subj "/C=$COUNTRY/ST=$STATE/L=$LOCATION/O=$ORGANIZATION/CN=$NAME" -new -x509 -days 7300 -sha256 -extensions v3_ca -out "$CA_FILE" -passin pass:$K_PWD
chmod 444 "$CA_FILE"
