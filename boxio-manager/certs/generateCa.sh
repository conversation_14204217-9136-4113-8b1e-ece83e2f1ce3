#/bin/bash

CA_PATH="./ca"

mkdir -p $CA_PATH

mkdir -p $CA_PATH/certs
mkdir -p $CA_PATH/crl
mkdir -p $CA_PATH/newcerts
mkdir -p $CA_PATH/private

chmod 700 $CA_PATH/private
touch $CA_PATH/index.txt
echo 1000 > $CA_PATH/serial

K_PWD="$(pwgen -cn $PASSWORD_LENGTH 8)"
echo $K_PWD

echo "Generate Key"
openssl genrsa -aes256 -passout pass:$K_PWD -out $CA_PATH/private/EDALab_CA.key.pem 4096
chmod 400 $CA_PATH/private/EDALab_CA.key.pem

echo "Generate Certs"
openssl req -config ./ca/openssl.cnf -key $CA_PATH/private/EDALab_CA.key.pem -subj "/C=IT/ST=Italy/L=Verona/O=edalab.it/CN=EDALAb_CA" -new -x509 -days 7300 -sha256 -extensions v3_ca -out $CA_PATH/certs/EDALab_CA.pem -passin pass:$K_PWD
chmod 444 $CA_PATH/certs/EDALab_CA.pem
