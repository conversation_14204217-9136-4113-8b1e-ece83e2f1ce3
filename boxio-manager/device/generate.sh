#!/bin/bash

function usage
{
    echo "Usage -d <deviceId> -u <username> -p <password> -o <output_path> [-a <keystore_alias>]"
    echo "Note:" 
    echo "- keystore_alias is server by default"
    exit 1
}

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

if [ $# -eq 0 ]; then
    usage
fi

while getopts "d:u:p:o:a:" OPT; do
    case $OPT in
        p)
            PASSWORD=${OPTARG}
        ;;
        d)
            DEVICEID=${OPTARG}
        ;;
        u)
            USERNAME=${OPTARG}
        ;;
        o)
            OUTPUT=${OPTARG}
        ;;
        a)
            ALIAS=${OPTARG}
        ;;
        ?)
            echo "Invalid option ?"
            usage
        ;;
        *)
            echo "Invalid option *"
            usage
        ;;
    esac
done

if [ -z "$DEVICEID" ]; then
    usage
fi

if [ -z "$USERNAME" ]; then
    usage
fi

if [ -z "$PASSWORD" ]; then
    usage
fi

if [ -z "$OUTPUT" ]; then
    usage
fi

if [ -z "$ALIAS" ]; then
    ALIAS="server"
fi

if ! [[ $USERNAME =~ ^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$ ]]; then
    echo "username not valid $USERNAME"
    exit 1
fi

PARENT_PATH="$OUTPUT"
HOST="$DEVICEID"
CERT_PATH="$PARENT_PATH/$DEVICEID"

if [[ -d $CERT_PATH ]]
then
    echo "Remove content of $CERT_PATH"
    #rm -r $CERT_PATH/*
fi

mkdir -p $CERT_PATH

SCRIPT_NAME=$(basename "$0")
CA_DIRECTORY="$SCRIPT_DIR/intermediate"
PRIVKEY_NAME="privkey.pem"
CERT_NAME="cert.pem"
FULLCHAIN_NAME="fullchain.pem"
CHAIN_FILE="$CA_DIRECTORY/chain.pem"
CHAIN_NAME="chain.pem"
KEYSTORE_NAME="keystore.p12"
PUBKEY_NAME="pubkey.pem"

if [[ ! -d "$CA_DIRECTORY" ]]
then
    echo "$CA_DIRECTORY does not exists in the same path of $SCRIPT_NAME"
    exit 1
fi

source $CA_DIRECTORY/config .
#config file contains CA_PATH and CA_KEY_FILE so ovveride it
CA_PATH="$CA_DIRECTORY/$CA_PATH"
CA_KEY_FILE="$CA_DIRECTORY/$CA_KEY_FILE"

PASSWORD_FILE="$CERT_PATH/password.txt"
:>$PASSWORD_FILE

DEVICE_FILE="$CERT_PATH/device.json"

export KEY_PWD="$(pwgen -cn $PASSWORD_LENGTH 15)"
export KEYSTORE_PWD="$(pwgen -cn $PASSWORD_LENGTH 15)"

echo "KEY_PWD=\""$(eval echo -n \"\$KEY_PWD\")"\"" >> $PASSWORD_FILE
echo "KEYSTORE_PWD=\""$(eval echo -n \"\$KEYSTORE_PWD\")"\"" >> $PASSWORD_FILE

echo "Generate key"
openssl genrsa -passout pass:$KEY_PWD -out $CERT_PATH/$PRIVKEY_NAME 4096

echo "Generate $HOST certificate"

SAN="[SAN]\nsubjectAltName=DNS:$HOST"

openssl req -new -sha256 -key $CERT_PATH/$PRIVKEY_NAME -subj "/C=IT/ST=Italy/L=Verona/O=edalab.it/CN=$HOST" -reqexts SAN -config <(cat /etc/ssl/openssl.cnf <(printf "$SAN")) -out "$CERT_PATH/cert.csr" -passin pass:$KEY_PWD

echo "Generate signed certificate with the certificate authority"
openssl x509 -req -in "$CERT_PATH/cert.csr" -CA "$CA_PATH" -CAkey "$CA_KEY_FILE" -CAcreateserial -extensions SAN -extfile <(cat /etc/ssl/openssl.cnf <(printf "$SAN")) -out "$CERT_PATH/$CERT_NAME" -days 3650 -sha256 -passin pass:$CA_KEY 2>/dev/null


echo "Generate fullchain"
cat $CERT_PATH/$CERT_NAME $CA_PATH > "$CERT_PATH/$FULLCHAIN_NAME"

echo "Generate chain"
cat $CHAIN_FILE > "$CERT_PATH/$CHAIN_NAME"

echo "Generate keystore"
openssl pkcs12 -name $ALIAS -inkey "$CERT_PATH/$PRIVKEY_NAME" -in "$CERT_PATH/$FULLCHAIN_NAME" -export -out "$CERT_PATH/$KEYSTORE_NAME" -passin pass:$KEY_PWD -passout pass:$KEYSTORE_PWD

echo "Generate publickey"
openssl x509 -in $CERT_PATH/$CERT_NAME -pubkey -noout > $CERT_PATH/$PUBKEY_NAME

printf '{"deviceId":"%s", "username":"%s", "password":"%s"}\n' "$DEVICEID" "$USERNAME" "$PASSWORD" > $DEVICE_FILE

#Generate md5
cd $CERT_PATH
md5sum * > "check.md5"
cd -

cd $PARENT_PATH
zip -r "$DEVICEID.zip" "$DEVICEID/"