import json
import random
import string
import os
import shutil


def random_generation(n):
    """
    Generate a random string of ascii and digits
    """
    return "".join(random.SystemRandom().choice(string.ascii_uppercase + string.digits) for _ in range(n))


def generate_password():
    return random_generation(16)


def select_from_enum(enum_values):
    print("Select one of the following options:")
    for i, option in enumerate(enum_values, 1):
        print(f"{i}. {option}")

    while True:
        choice = input("Enter the number corresponding to your choice: ")
        if choice.isdigit() and 1 <= int(choice) <= len(enum_values):
            return enum_values[int(choice) - 1]
        else:
            print("Invalid choice. Please enter a valid number.")


def retrieveVersionFromFile(name):
    with open("release/config_file.json", "r", encoding="utf-8") as file:
        data = json.load(file)
    for service in data["services"]:
        if service["name"] == name:
            version = service["version"]
            if "/" in version:
                return version.replace("/", ":")
            return version


def get_user_input(prop, example):
    if "enum" in prop:
        print(f"Enter value for {prop['name']} ")
        return select_from_enum(prop["enum"])
    else:
        return input(f"Enter value for {prop['name']}:{[example]} ")


def create_backup_file(file_path):
    if os.path.exists(file_path):
        backup_file_path = file_path + ".bak"
        shutil.copyfile(file_path, backup_file_path)
        print(f"Backup created: {backup_file_path}")


def generate_env_from_json(json_file, existing_env=None):
    with open(json_file, "r") as f:
        data = json.load(f)

    env_lines = []

    for section in data:
        env_lines.append(f"\n# {section['subsection']}")
        for prop in section["properties"]:
            if existing_env and prop["name"] in existing_env:
                if prop.get("default"):
                    existing_value = existing_env[prop["name"]]
                    if existing_value != prop["default"]:
                        choice = input(
                            f"The default value for {prop['name']} has changed. It was '{existing_value}'  "
                            f"Do you want to use the new value '{prop['default']}'? (y/n): "
                        )
                        if choice.lower() == "y":
                            value = prop["default"]
                        else:
                            value = existing_value
                    else:
                        value = prop["default"]
                else:
                    value = existing_env[prop["name"]]
            elif prop.get("userInput", False):
                example = prop.get("example")
                value = get_user_input(prop, example)
            elif prop.get("default"):
                value = prop["default"]
            elif prop.get("autogenerated", True):
                value = generate_password()

            if prop.get("versionFileReference"):
                value = retrieveVersionFromFile(prop["versionFileReference"])
            env_lines.append(f"{prop['name']}={value}")
    if existing_env:
        missing_keys = set(existing_env.keys()) - {prop["name"] for section in data for prop in section["properties"]}
        for key in missing_keys:
            print(key, " is missing")
    return "\n".join(env_lines)


# Path to your JSON file
json_file_path = "env.json"
env_file_path = ".env"
create_backup_file(env_file_path)
if os.path.exists(env_file_path):
    # Load existing environment file
    with open(env_file_path, "r") as f:
        existing_env = {}
        for line in f:
            line = line.strip()
            if line and not line.startswith("#"):
                key, value = line.split("=", 1)
                existing_env[key] = value

    # Generate updated content
    env_content = generate_env_from_json(json_file_path, existing_env)
else:
    # Generate env file content
    env_content = generate_env_from_json(json_file_path)

# Write the content to the env file
with open(env_file_path, "w") as f:
    f.write(env_content)

print("Environment file generated successfully!")
