#!/bin/sh
set -eu

# If we're in “jx” mode, point INCLUDE_JITSI at jitsi.conf; else leave it empty.
if [ "${NGINX_MODE:-standard}" != "standard" ]; then
    export JITSI_LOCATIONS_INCLUDE="include /etc/nginx/conf.d/jitsi.conf.template;"
else
    export JITSI_LOCATIONS_INCLUDE=""
fi

# Render the real default.conf
envsubst '$JITSI_LOCATIONS_INCLUDE $HOST_CERTS' \
    </etc/nginx/conf.d/base.conf.template \
    >/etc/nginx/conf.d/default.conf

echo "--- Final generated Nginx configuration ---"
cat /etc/nginx/conf.d/default.conf
echo "------------------------------------------"

# Start nginx
exec nginx -g 'daemon off;'
