server {
    listen 9997 ssl;
    server_name _;

    resolver 127.0.0.11 ipv6=off valid=10s;
    resolver_timeout 10s;

    # SSL configuration
    ssl_certificate /etc/ssl/private/boxio/server/fullchain.pem;
    ssl_certificate_key /etc/ssl/private/boxio/server/privkey.pem;

    location ~ /am(/.*)?$ {
        set $boxiomanager boxio-manager:8081;
        proxy_pass http://$boxiomanager;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Host $server_name;
        proxy_set_header   X-SSL-CERT $ssl_client_escaped_cert;
        proxy_set_header   X-SSL-DN $ssl_client_s_dn;
        proxy_set_header   X-SSL-VALID $ssl_client_verify;
        client_max_body_size 50M;
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Credentials' 'false';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'host, connection, accept, origin, authorization,user-agent, referer, accept-encoding, accept-language, x-csrftoken, x-custom-header, x-requested-with, content-type';
                add_header 'Access-Control-Max-Age' 3600;
        }

    }
}


server {
    listen 443 ssl default_server;
    server_name _;

    resolver 127.0.0.11 ipv6=off valid=10s;
    resolver_timeout 10s;

    root /usr/share/nginx/html;
    index index.html index.htm index.nginx-debian.html;
    ssl_certificate /etc/ssl/private/boxio/server/fullchain.pem;
    ssl_certificate_key /etc/ssl/private/boxio/server/privkey.pem;
    error_page 497 301 =307 https://$host:$server_port$request_uri;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE+AESGCM:ECDHE+AES256:ECDHE+AES128:!AES128-SHA:!AES256-SHA:!DES-CBC3-SHA:!RC4:!MD5:!SHA1:!SHA256:!SHA384:!DSS:!aNULL';
    # add_header Content-Security-Policy "default-src 'self'; script-src https://unpkg.com 'self' 'unsafe-inline'; object-src 'none'; style-src 'self' 'unsafe-inline' https://unpkg.com https://fonts.googleapis.com; img-src 'self' data: https://b.tile.openstreetmap.org https://c.tile.openstreetmap.org https://a.tile.openstreetmap.org https://unpkg.com;; media-src 'self'; frame-src 'self'; font-src https://fonts.gstatic.com 'self'; connect-src 'self'; base-uri 'self'; worker-src 'self'; child-src 'self'; frame-ancestors 'none'; upgrade-insecure-requests; form-action 'self'";
    add_header X-Frame-Options "deny";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Cache-control "no-store";
    add_header Referrer-Policy "no-referrer";

    # Expose /media directory for web-editor files
    location /media {
        root /;
    }

    location / {
            try_files $uri $uri/ /index.html;
    }


    # Assistant service
    location ~ /as(/.*)?$ {
        rewrite  ^/as/(.*) /$1 break;
        set $assistantservice assistant-service;
        proxy_pass         http://$assistantservice:3000;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Host $server_name;
        client_max_body_size 50M;
    }


    # Boxio manager
    location ~ /am(/.*)?$ {
        set $boxiomanager boxio-manager:8081;
        proxy_pass http://$boxiomanager;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Host $server_name;
        proxy_set_header   X-SSL-CERT $ssl_client_escaped_cert;
        proxy_set_header   X-SSL-DN $ssl_client_s_dn;
        proxy_set_header   X-SSL-VALID $ssl_client_verify;
        client_max_body_size 50M;
	    if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Credentials' 'false';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'host, connection, accept, origin, authorization,user-agent, referer, accept-encoding, accept-language, x-csrftoken, x-custom-header, x-requested-with, content-type';
                add_header 'Access-Control-Max-Age' 3600;
        }
    }

    # MQTT
    location ~ /mqtt(/.*)?$ {
        set $mqtt mqtt;
        proxy_pass http://$mqtt:8083;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Configuration manager websocket
    location = /cm/macAddress {
        set $configurationmanager configuration-manager;
        proxy_pass         http://$configurationmanager:8089;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Host $server_name;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location ^~ /socket {
        set $configurationmanager configuration-manager;
        proxy_pass         http://$configurationmanager:8089;
        proxy_redirect off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        if ($request_method = 'OPTIONS') {
           add_header 'Access-Control-Allow-Credentials' 'false';
           add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
           add_header 'Access-Control-Allow-Headers' 'host, connection, accept, origin, authorization,user-agent, referer, accept-encoding, accept-language, x-csrftoken, x-custom-header, x-requested-with, content-type';
           add_header 'Access-Control-Max-Age' 3600;
         }
     }


    # Event manager
    location ~ /em(/.*)?$ {
        rewrite  ^/em/(.*) /$1 break;
        set $eventmanager event-manager;
        proxy_pass         http://$eventmanager:3000;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Host $server_name;
        client_max_body_size 50M;
    }




}

server {
    listen 443 ssl;
    server_name ${HOST};
    resolver 127.0.0.11 ipv6=off valid=10s;
    resolver_timeout 10s;
    root /usr/share/nginx/html;
    index index.html index.htm index.nginx-debian.html;
    ssl_certificate /etc/ssl/private/boxio/server/fullchain.pem;
    ssl_certificate_key /etc/ssl/private/boxio/server/privkey.pem;
    error_page 497 301 =307 https://$host:$server_port$request_uri;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers 'ECDHE+AESGCM:ECDHE+AES256:ECDHE+AES128:!AES128-SHA:!AES256-SHA:!DES-CBC3-SHA:!RC4:!MD5:!SHA1:!SHA256:!SHA384:!DSS:!aNULL';
    ssl_verify_client optional_no_ca;
    # add_header Content-Security-Policy "default-src 'self'; script-src https://unpkg.com 'self' 'unsafe-inline'; object-src 'none'; style-src 'self' 'unsafe-inline' https://unpkg.com https://fonts.googleapis.com; img-src 'self' data: https://b.tile.openstreetmap.org https://c.tile.openstreetmap.org https://a.tile.openstreetmap.org https://unpkg.com;; media-src 'self'; frame-src 'self'; font-src https://fonts.gstatic.com 'self'; connect-src 'self'; base-uri 'self'; worker-src 'self'; child-src 'self'; frame-ancestors 'none'; upgrade-insecure-requests; form-action 'self'";
    add_header X-Frame-Options "deny";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    add_header Cache-control "no-store";
    add_header Referrer-Policy "no-referrer";
    # Expose /media directory for web-editor files
    location /media {
        root /;
    }
    location / {
            try_files $uri $uri/ /index.html;
    }
    # Boxio manager
    location ~ /am(/.*)?$ {
        set $boxiomanager boxio-manager:8081;
        proxy_pass http://$boxiomanager;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Host $server_name;
        proxy_set_header   X-SSL-CERT $ssl_client_escaped_cert;
        proxy_set_header   X-SSL-DN $ssl_client_s_dn;
        proxy_set_header   X-SSL-VALID $ssl_client_verify;
        client_max_body_size 50M;
        if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Credentials' 'false';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'host, connection, accept, origin, authorization,user-agent, referer, accept-encoding, accept-language, x-csrftoken, x-custom-header, x-requested-with, content-type';
                add_header 'Access-Control-Max-Age' 3600;
        }
    }
    ${JITSI_LOCATIONS_INCLUDE}
 }
