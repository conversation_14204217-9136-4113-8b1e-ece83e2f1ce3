
location /jitsi {
    ssi on;
    proxy_pass http://jitsi-9220-web-1:80;
    proxy_set_header X-Forwarded-For $remote_addr;
    proxy_set_header Host $http_host;
}

location ~ ^/(libs|css|static|images|fonts|lang|sounds|connection_optimization|.well-known)/(.*)$ {
    proxy_pass http://jitsi-9220-web-1:80;
    proxy_set_header X-Forwarded-For $remote_addr;
    proxy_set_header Host $http_host;
}

# BOSH
location /http-bind {
    proxy_pass http://jitsi-9220-prosody-1:5280/http-bind;
    proxy_set_header X-Forwarded-For $remote_addr;
    proxy_set_header Host $http_host;
}

# xmpp websockets
location /jitsi/jitsi/xmpp-websocket {
    proxy_pass              http://jitsi-9220-prosody-1:5280/xmpp-websocket;
    proxy_http_version      1.1;
    proxy_set_header        Upgrade $http_upgrade;
    proxy_set_header        Connection "upgrade";
    proxy_set_header        Host $host;
    tcp_nodelay             on;
}
